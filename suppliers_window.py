import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Supplier

class SuppliersWindow:
    def __init__(self, parent):
        self.parent = parent
        self.supplier_model = Supplier()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة الموردين")
        self.window.geometry("900x600")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_suppliers()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=10, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة إدارة الموردين"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#f39c12', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة الموردين", 
                              font=self.arabic_font_bold, fg='white', bg='#f39c12')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار النموذج (يسار)
        form_frame = tk.LabelFrame(main_frame, text="بيانات المورد", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        form_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        self.create_supplier_form(form_frame)
        
        # إطار القائمة (يمين)
        list_frame = tk.LabelFrame(main_frame, text="قائمة الموردين", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        self.create_suppliers_list(list_frame)
    
    def create_supplier_form(self, parent):
        """إنشاء نموذج إدخال المورد"""
        # اسم المورد
        tk.Label(parent, text="اسم المورد:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        self.name_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # رقم الهاتف
        tk.Label(parent, text="رقم الهاتف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='e', padx=5, pady=5)
        self.phone_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.phone_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # العنوان
        tk.Label(parent, text="العنوان:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=2, column=0, sticky='ne', padx=5, pady=5)
        self.address_text = tk.Text(parent, font=self.arabic_font, width=25, height=3)
        self.address_text.grid(row=2, column=1, padx=5, pady=5)
        
        # البريد الإلكتروني
        tk.Label(parent, text="البريد الإلكتروني:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=3, column=0, sticky='e', padx=5, pady=5)
        self.email_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.email_entry.grid(row=3, column=1, padx=5, pady=5)
        
        # الملاحظات
        tk.Label(parent, text="الملاحظات:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=4, column=0, sticky='ne', padx=5, pady=5)
        self.notes_text = tk.Text(parent, font=self.arabic_font, width=25, height=4)
        self.notes_text.grid(row=4, column=1, padx=5, pady=5)
        
        # الأزرار
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        self.add_btn = tk.Button(buttons_frame, text="إضافة", font=self.arabic_font,
                                bg='#27ae60', fg='white', width=10,
                                command=self.add_supplier)
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = tk.Button(buttons_frame, text="تحديث", font=self.arabic_font,
                                   bg='#3498db', fg='white', width=10,
                                   command=self.update_supplier, state='disabled')
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = tk.Button(buttons_frame, text="حذف", font=self.arabic_font,
                                   bg='#e74c3c', fg='white', width=10,
                                   command=self.delete_supplier, state='disabled')
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = tk.Button(buttons_frame, text="مسح", font=self.arabic_font,
                                  bg='#95a5a6', fg='white', width=10,
                                  command=self.clear_form)
        self.clear_btn.pack(side='left', padx=5)

        self.account_btn = tk.Button(buttons_frame, text="كشف حساب", font=self.arabic_font,
                                    bg='#8e44ad', fg='white', width=10,
                                    command=self.show_account_statement, state='disabled')
        self.account_btn.pack(side='left', padx=5)
    
    def create_suppliers_list(self, parent):
        """إنشاء قائمة الموردين"""
        # إطار البحث
        search_frame = tk.Frame(parent, bg='#f0f0f0')
        search_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(search_frame, text="البحث:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.search_entry = tk.Entry(search_frame, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_suppliers)
        
        search_btn = tk.Button(search_frame, text="بحث", font=self.arabic_font,
                              bg='#34495e', fg='white', command=self.search_suppliers)
        search_btn.pack(side='left', padx=5)
        
        # جدول الموردين
        columns = ('ID', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'الرصيد')
        self.suppliers_tree = ttk.Treeview(parent, columns=columns, show='headings', height=20)
        
        # تكوين الأعمدة
        self.suppliers_tree.heading('ID', text='ID')
        self.suppliers_tree.heading('الاسم', text='الاسم')
        self.suppliers_tree.heading('الهاتف', text='الهاتف')
        self.suppliers_tree.heading('البريد الإلكتروني', text='البريد الإلكتروني')
        self.suppliers_tree.heading('الرصيد', text='الرصيد')
        
        # تحديد عرض الأعمدة
        self.suppliers_tree.column('ID', width=50)
        self.suppliers_tree.column('الاسم', width=150)
        self.suppliers_tree.column('الهاتف', width=120)
        self.suppliers_tree.column('البريد الإلكتروني', width=180)
        self.suppliers_tree.column('الرصيد', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.suppliers_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # ربط حدث النقر على المورد
        self.suppliers_tree.bind('<ButtonRelease-1>', self.on_supplier_select)
        
        # متغير لحفظ ID المورد المحدد
        self.selected_supplier_id = None
    
    def load_suppliers(self):
        """تحميل الموردين"""
        # مسح الجدول
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # تحميل الموردين
        suppliers = self.supplier_model.get_all_suppliers()
        for supplier in suppliers:
            self.suppliers_tree.insert('', 'end', values=(
                supplier[0],  # ID
                supplier[1],  # name
                supplier[2] or '',  # phone
                supplier[4] or '',  # email
                f"{supplier[5]:.2f}"  # balance
            ))
    
    def search_suppliers(self, event=None):
        """البحث في الموردين"""
        search_term = self.search_entry.get()
        
        # مسح الجدول
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # البحث في الموردين
        suppliers = self.supplier_model.get_all_suppliers()
        for supplier in suppliers:
            if (search_term.lower() in supplier[1].lower() or 
                (supplier[2] and search_term in supplier[2]) or
                (supplier[4] and search_term.lower() in supplier[4].lower())):
                
                self.suppliers_tree.insert('', 'end', values=(
                    supplier[0],  # ID
                    supplier[1],  # name
                    supplier[2] or '',  # phone
                    supplier[4] or '',  # email
                    f"{supplier[5]:.2f}"  # balance
                ))
    
    def on_supplier_select(self, event):
        """عند تحديد مورد من القائمة"""
        selection = self.suppliers_tree.selection()
        if selection:
            item = self.suppliers_tree.item(selection[0])
            supplier_id = item['values'][0]
            
            # جلب بيانات المورد
            suppliers = self.supplier_model.get_all_suppliers()
            supplier = None
            for s in suppliers:
                if s[0] == supplier_id:
                    supplier = s
                    break
            
            if supplier:
                self.fill_form_with_supplier(supplier)
                self.selected_supplier_id = supplier_id
                
                # تفعيل أزرار التحديث والحذف وكشف الحساب
                self.update_btn.config(state='normal')
                self.delete_btn.config(state='normal')
                self.account_btn.config(state='normal')
    
    def fill_form_with_supplier(self, supplier):
        """ملء النموذج ببيانات المورد"""
        self.clear_form()
        
        self.name_entry.insert(0, supplier[1])
        if supplier[2]:
            self.phone_entry.insert(0, supplier[2])
        if supplier[3]:
            self.address_text.insert('1.0', supplier[3])
        if supplier[4]:
            self.email_entry.insert(0, supplier[4])
        if supplier[6]:
            self.notes_text.insert('1.0', supplier[6])
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_entry.delete(0, 'end')
        self.phone_entry.delete(0, 'end')
        self.address_text.delete('1.0', 'end')
        self.email_entry.delete(0, 'end')
        self.notes_text.delete('1.0', 'end')
        
        self.selected_supplier_id = None
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        self.account_btn.config(state='disabled')
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم المورد")
            return False
        return True
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        if not self.validate_form():
            return
        
        # جمع البيانات
        name = self.name_entry.get().strip()
        phone = self.phone_entry.get().strip()
        address = self.address_text.get('1.0', 'end').strip()
        email = self.email_entry.get().strip()
        notes = self.notes_text.get('1.0', 'end').strip()
        
        # إضافة المورد
        success, supplier_id, message = self.supplier_model.add_supplier(
            name, phone, address, email, notes
        )
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_suppliers()
        else:
            messagebox.showerror("خطأ", message)
    
    def update_supplier(self):
        """تحديث المورد"""
        if not self.selected_supplier_id:
            messagebox.showerror("خطأ", "يجب تحديد مورد للتحديث")
            return
        
        if not self.validate_form():
            return
        
        messagebox.showinfo("قريباً", "ميزة تحديث المورد قيد التطوير")
    
    def delete_supplier(self):
        """حذف المورد"""
        if not self.selected_supplier_id:
            messagebox.showerror("خطأ", "يجب تحديد مورد للحذف")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المورد؟"):
            messagebox.showinfo("قريباً", "ميزة حذف المورد قيد التطوير")

    def show_account_statement(self):
        """عرض كشف حساب المورد"""
        if not self.selected_supplier_id:
            messagebox.showerror("خطأ", "يجب تحديد مورد لعرض كشف حسابه")
            return

        from accounts_window import AccountsWindow
        AccountsWindow(self.window, 'supplier', self.selected_supplier_id)
