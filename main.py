import tkinter as tk
from tkinter import ttk, messagebox, font
import sys
import os

# إضافة مسار المشروع لاستيراد الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Product, Category, Customer, Supplier, Sale, Purchase
from database import db
from products_window import ProductsWindow
from pos_window import POSWindow
from customers_window import CustomersWindow
from suppliers_window import SuppliersWindow
from purchase_window import PurchaseWindow
from categories_window import CategoriesWindow
from reports_window import ReportsWindow
from expenses_window import ExpensesWindow
from auth_system import auth_system
from login_window import LoginWindow, UserManagementWindow
from notifications_system import notification_system, NotificationsWindow
from advanced_inventory import AdvancedInventoryWindow
from ui_enhancements import ui_enhancements

class ShopManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المحل")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء القوائم
        self.create_menu()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # تحديث إحصائيات المخزون
        self.update_stock_alerts()

        # إعداد اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # محاولة استخدام خط عربي
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=12, weight="bold")
        except:
            # في حالة عدم توفر الخط، استخدام الخط الافتراضي
            self.arabic_font = font.Font(size=10)
            self.arabic_font_large = font.Font(size=12, weight="bold")
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="ملف", menu=file_menu, font=self.arabic_font)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_command(label="استعادة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المنتجات", menu=products_menu, font=self.arabic_font)
        products_menu.add_command(label="إدارة المنتجات", command=self.open_products_window)
        products_menu.add_command(label="إدارة التصنيفات", command=self.open_categories_window)
        products_menu.add_command(label="تقرير المخزون", command=self.show_stock_report)
        products_menu.add_separator()
        products_menu.add_command(label="المخزون المتقدم", command=self.open_advanced_inventory)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المبيعات", menu=sales_menu, font=self.arabic_font)
        sales_menu.add_command(label="نقطة البيع", command=self.open_pos_window)
        sales_menu.add_command(label="تقرير المبيعات", command=self.show_sales_report)
        
        # قائمة المشتريات
        purchases_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المشتريات", menu=purchases_menu, font=self.arabic_font)
        purchases_menu.add_command(label="فاتورة شراء", command=self.open_purchase_window)
        purchases_menu.add_command(label="تقرير المشتريات", command=self.show_purchases_report)
        
        # قائمة العملاء والموردين
        contacts_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="العملاء والموردين", menu=contacts_menu, font=self.arabic_font)
        contacts_menu.add_command(label="إدارة العملاء", command=self.open_customers_window)
        contacts_menu.add_command(label="إدارة الموردين", command=self.open_suppliers_window)
        
        # قائمة المالية
        finance_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المالية", menu=finance_menu, font=self.arabic_font)
        finance_menu.add_command(label="المصروفات والإيرادات", command=self.open_expenses_window)
        finance_menu.add_separator()
        finance_menu.add_command(label="تقرير الأرباح والخسائر", command=self.show_profit_loss_report)

        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="التقارير", menu=reports_menu, font=self.arabic_font)
        reports_menu.add_command(label="جميع التقارير", command=self.show_reports_menu)
        reports_menu.add_separator()
        reports_menu.add_command(label="تقرير المبيعات", command=self.show_sales_report)
        reports_menu.add_command(label="تقرير المشتريات", command=self.show_purchases_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.show_stock_report)

        # قائمة النظام (للمديرين فقط)
        if auth_system.has_permission('view_users'):
            system_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
            menubar.add_cascade(label="النظام", menu=system_menu, font=self.arabic_font)
            system_menu.add_command(label="إدارة المستخدمين", command=self.open_user_management)
            system_menu.add_command(label="سجل النشاطات", command=self.view_activity_log)
            system_menu.add_separator()
            system_menu.add_command(label="إعدادات النظام", command=self.system_settings)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="نظام إدارة المحل", 
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء الأزرار الرئيسية
        self.create_main_buttons(buttons_frame)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.root, text="إحصائيات سريعة", 
                                   font=self.arabic_font, bg='#f0f0f0')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        self.create_stats_display(stats_frame)
    
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # الصف الأول من الأزرار
        row1_frame = tk.Frame(parent, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=10)
        
        # زر نقطة البيع
        pos_btn = ui_enhancements.create_styled_button(
            row1_frame, "نقطة البيع", style='success', icon='cart',
            font=self.arabic_font_large, width=15, height=3,
            command=self.open_pos_window
        )
        pos_btn.pack(side='left', padx=10)
        
        # زر إدارة المنتجات
        products_btn = ui_enhancements.create_styled_button(
            row1_frame, "إدارة المنتجات", style='primary', icon='product',
            font=self.arabic_font_large, width=15, height=3,
            command=self.open_products_window
        )
        products_btn.pack(side='left', padx=10)
        
        # زر المشتريات
        purchase_btn = ui_enhancements.create_styled_button(
            row1_frame, "فاتورة شراء", style='danger', icon='import',
            font=self.arabic_font_large, width=15, height=3,
            command=self.open_purchase_window
        )
        purchase_btn.pack(side='left', padx=10)
        
        # الصف الثاني من الأزرار
        row2_frame = tk.Frame(parent, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=10)
        
        # زر العملاء
        customers_btn = tk.Button(row2_frame, text="إدارة العملاء", font=self.arabic_font_large,
                                 bg='#9b59b6', fg='white', width=15, height=3,
                                 command=self.open_customers_window)
        customers_btn.pack(side='left', padx=10)
        
        # زر الموردين
        suppliers_btn = tk.Button(row2_frame, text="إدارة الموردين", font=self.arabic_font_large,
                                 bg='#f39c12', fg='white', width=15, height=3,
                                 command=self.open_suppliers_window)
        suppliers_btn.pack(side='left', padx=10)
        
        # زر التقارير
        reports_btn = tk.Button(row2_frame, text="التقارير", font=self.arabic_font_large,
                               bg='#34495e', fg='white', width=15, height=3,
                               command=self.show_reports_menu)
        reports_btn.pack(side='left', padx=10)

        # الصف الثالث من الأزرار
        row3_frame = tk.Frame(parent, bg='#f0f0f0')
        row3_frame.pack(fill='x', pady=10)

        # زر الإشعارات مع عداد
        self.create_notifications_button(row3_frame)

    def create_notifications_button(self, parent):
        """إنشاء زر الإشعارات مع عداد"""
        # إطار زر الإشعارات
        notifications_frame = tk.Frame(parent, bg='#f0f0f0')
        notifications_frame.pack(side='left', padx=10)

        # حساب عدد الإشعارات
        notification_count = notification_system.get_notification_count()
        high_priority_count = notification_system.get_notification_count('high')

        # تحديد لون الزر حسب الأولوية
        if high_priority_count > 0:
            btn_color = '#e74c3c'  # أحمر للإشعارات عالية الأولوية
        elif notification_count > 0:
            btn_color = '#f39c12'  # برتقالي للإشعارات العادية
        else:
            btn_color = '#95a5a6'  # رمادي لعدم وجود إشعارات

        # النص على الزر
        btn_text = f"الإشعارات ({notification_count})"

        # زر الإشعارات
        self.notifications_btn = tk.Button(notifications_frame, text=btn_text,
                                          font=self.arabic_font_large,
                                          bg=btn_color, fg='white', width=15, height=3,
                                          command=self.open_notifications_window)
        self.notifications_btn.pack()

        # إضافة تلميح إذا كان هناك إشعارات عالية الأولوية
        if high_priority_count > 0:
            tooltip_text = f"لديك {high_priority_count} إشعار عالي الأولوية!"
            self.create_tooltip(self.notifications_btn, tooltip_text)

    def create_tooltip(self, widget, text):
        """إنشاء تلميح للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text, font=self.arabic_font,
                           bg='#2c3e50', fg='white', relief='solid', bd=1)
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def create_stats_display(self, parent):
        """إنشاء عرض الإحصائيات"""
        stats_inner_frame = tk.Frame(parent, bg='#f0f0f0')
        stats_inner_frame.pack(fill='x', padx=10, pady=10)
        
        # إجمالي المنتجات
        self.total_products_label = tk.Label(stats_inner_frame, text="إجمالي المنتجات: 0", 
                                           font=self.arabic_font, bg='#f0f0f0')
        self.total_products_label.pack(side='left', padx=20)
        
        # المنتجات منخفضة المخزون
        self.low_stock_label = tk.Label(stats_inner_frame, text="منتجات منخفضة المخزون: 0", 
                                      font=self.arabic_font, bg='#f0f0f0', fg='red')
        self.low_stock_label.pack(side='left', padx=20)
        
        # مبيعات اليوم
        self.today_sales_label = tk.Label(stats_inner_frame, text="مبيعات اليوم: 0", 
                                        font=self.arabic_font, bg='#f0f0f0')
        self.today_sales_label.pack(side='left', padx=20)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز", bd=1, relief='sunken', 
                                  anchor='w', font=self.arabic_font)
        self.status_bar.pack(side='bottom', fill='x')
    
    def update_stock_alerts(self):
        """تحديث تنبيهات المخزون والإحصائيات"""
        try:
            product_model = Product()
            
            # إجمالي المنتجات
            all_products = product_model.get_all_products()
            total_products = len(all_products)
            self.total_products_label.config(text=f"إجمالي المنتجات: {total_products}")
            
            # المنتجات منخفضة المخزون
            low_stock_products = product_model.get_low_stock_products()
            low_stock_count = len(low_stock_products)
            self.low_stock_label.config(text=f"منتجات منخفضة المخزون: {low_stock_count}")
            
            if low_stock_count > 0:
                self.low_stock_label.config(fg='red')
                self.status_bar.config(text=f"تحذير: {low_stock_count} منتج منخفض المخزون")
            else:
                self.low_stock_label.config(fg='green')
                self.status_bar.config(text="جاهز")
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
    
    # وظائف فتح النوافذ (ستتم إضافتها لاحقاً)
    def open_pos_window(self):
        if auth_system.has_permission('pos_sales'):
            POSWindow(self.root)
        else:
            messagebox.showerror("خطأ", "ليس لديك صلاحية لاستخدام نقطة البيع")
    
    def open_products_window(self):
        if auth_system.has_permission('view_products'):
            ProductsWindow(self.root)
        else:
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض هذه النافذة")
    
    def open_categories_window(self):
        CategoriesWindow(self.root)
    
    def open_purchase_window(self):
        if auth_system.has_permission('manage_purchases'):
            PurchaseWindow(self.root)
        else:
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المشتريات")
    
    def open_customers_window(self):
        CustomersWindow(self.root)

    def open_suppliers_window(self):
        SuppliersWindow(self.root)
    
    def show_stock_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("inventory")
        reports_window.generate_report()

    def show_sales_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("sales")

    def show_purchases_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("purchases")

    def show_profit_loss_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("profit_loss")

    def show_expenses_report(self):
        ReportsWindow(self.root)

    def open_expenses_window(self):
        ExpensesWindow(self.root)

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من تسجيل الخروج؟"):
            auth_system.logout()
            self.root.destroy()
            main()  # إعادة تشغيل التطبيق

    def open_user_management(self):
        """فتح نافذة إدارة المستخدمين"""
        UserManagementWindow(self.root)

    def view_activity_log(self):
        """عرض سجل النشاطات"""
        messagebox.showinfo("قريباً", "ميزة سجل النشاطات قيد التطوير")

    def system_settings(self):
        """إعدادات النظام"""
        messagebox.showinfo("قريباً", "ميزة إعدادات النظام قيد التطوير")

    def open_notifications_window(self):
        """فتح نافذة الإشعارات"""
        NotificationsWindow(self.root)
        # تحديث زر الإشعارات بعد فتح النافذة
        self.update_notifications_button()

    def update_notifications_button(self):
        """تحديث زر الإشعارات"""
        # إعادة تحميل الإشعارات
        notification_system.load_notifications()

        # حساب عدد الإشعارات الجديد
        notification_count = notification_system.get_notification_count()
        high_priority_count = notification_system.get_notification_count('high')

        # تحديد لون الزر حسب الأولوية
        if high_priority_count > 0:
            btn_color = '#e74c3c'  # أحمر للإشعارات عالية الأولوية
        elif notification_count > 0:
            btn_color = '#f39c12'  # برتقالي للإشعارات العادية
        else:
            btn_color = '#95a5a6'  # رمادي لعدم وجود إشعارات

        # تحديث النص واللون
        btn_text = f"الإشعارات ({notification_count})"
        self.notifications_btn.config(text=btn_text, bg=btn_color)

    def open_advanced_inventory(self):
        """فتح نافذة المخزون المتقدم"""
        if auth_system.has_permission('view_inventory'):
            AdvancedInventoryWindow(self.root)
        else:
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض هذه النافذة")

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصارات النوافذ الرئيسية
        self.root.bind('<F1>', lambda e: self.open_pos_window())  # نقطة البيع
        self.root.bind('<F2>', lambda e: self.open_products_window())  # المنتجات
        self.root.bind('<F3>', lambda e: self.open_purchase_window())  # المشتريات
        self.root.bind('<F4>', lambda e: self.open_customers_window())  # العملاء
        self.root.bind('<F5>', lambda e: self.update_stock_alerts())  # تحديث
        self.root.bind('<F6>', lambda e: self.open_suppliers_window())  # الموردين
        self.root.bind('<F7>', lambda e: self.show_reports_menu())  # التقارير
        self.root.bind('<F8>', lambda e: self.open_expenses_window())  # المصروفات
        self.root.bind('<F9>', lambda e: self.open_notifications_window())  # الإشعارات

        # اختصارات عامة
        self.root.bind('<Control-q>', lambda e: self.root.quit())  # خروج
        self.root.bind('<Control-l>', lambda e: self.logout())  # تسجيل خروج
        self.root.bind('<Control-b>', lambda e: self.backup_database())  # نسخة احتياطية

        # اختصارات المديرين
        if auth_system.has_permission('view_users'):
            self.root.bind('<Control-u>', lambda e: self.open_user_management())  # إدارة المستخدمين

        # عرض مساعدة الاختصارات
        self.root.bind('<F12>', lambda e: self.show_shortcuts_help())

    def show_shortcuts_help(self):
        """عرض مساعدة اختصارات لوحة المفاتيح"""
        help_window = tk.Toplevel(self.root)
        help_window.title("اختصارات لوحة المفاتيح")
        help_window.geometry("500x600")
        help_window.configure(bg='#f0f0f0')

        # إطار العنوان
        title_frame = tk.Frame(help_window, bg='#3498db', height=50)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="اختصارات لوحة المفاتيح",
                              font=self.arabic_font_large, fg='white', bg='#3498db')
        title_label.pack(expand=True)

        # إطار المحتوى
        content_frame = tk.Frame(help_window, bg='#f0f0f0')
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قائمة الاختصارات
        shortcuts_text = """
اختصارات النوافذ الرئيسية:
F1 - نقطة البيع
F2 - إدارة المنتجات
F3 - فاتورة شراء
F4 - إدارة العملاء
F5 - تحديث البيانات
F6 - إدارة الموردين
F7 - التقارير
F8 - المصروفات والإيرادات
F9 - الإشعارات
F12 - عرض هذه المساعدة

اختصارات عامة:
Ctrl+Q - خروج من البرنامج
Ctrl+L - تسجيل الخروج
Ctrl+B - إنشاء نسخة احتياطية

اختصارات المديرين:
Ctrl+U - إدارة المستخدمين

نصائح:
• استخدم مفتاح Tab للتنقل بين الحقول
• استخدم Enter لتأكيد الإدخال
• استخدم Escape للإلغاء
• استخدم Ctrl+F للبحث (في النوافذ المدعومة)
        """

        text_widget = tk.Text(content_frame, font=self.arabic_font,
                             bg='white', fg='#2c3e50', relief='flat',
                             wrap='word', state='normal')
        text_widget.pack(fill='both', expand=True, pady=10)

        text_widget.insert('1.0', shortcuts_text)
        text_widget.config(state='disabled')

        # زر الإغلاق
        close_btn = ui_enhancements.create_styled_button(
            content_frame, "إغلاق", style='secondary', icon='close',
            command=help_window.destroy
        )
        close_btn.pack(pady=10)
    
    def show_reports_menu(self):
        ReportsWindow(self.root)
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        success, message = db.backup_database()
        if success:
            messagebox.showinfo("نجح", message)
        else:
            messagebox.showerror("خطأ", message)
    
    def restore_database(self):
        """استعادة قاعدة البيانات"""
        from tkinter import filedialog
        backup_file = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        if backup_file:
            success, message = db.restore_database(backup_file)
            if success:
                messagebox.showinfo("نجح", message)
                self.update_stock_alerts()
            else:
                messagebox.showerror("خطأ", message)

def main():
    # إخفاء النافذة الرئيسية مؤقتاً
    root = tk.Tk()
    root.withdraw()

    # عرض نافذة تسجيل الدخول
    def on_login_success():
        root.deiconify()  # إظهار النافذة الرئيسية
        app = ShopManagementApp(root)

        # إضافة معلومات المستخدم الحالي في شريط الحالة
        current_user = auth_system.get_current_user()
        if current_user:
            user_info = f"المستخدم: {current_user['full_name']} ({current_user['role']})"
            app.status_label.config(text=user_info)

    login_window = LoginWindow(on_login_success)
    login_successful = login_window.show()

    if login_successful:
        root.mainloop()
    else:
        root.destroy()

if __name__ == "__main__":
    main()
