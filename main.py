import tkinter as tk
from tkinter import ttk, messagebox, font
import sys
import os

# إضافة مسار المشروع لاستيراد الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Product, Category, Customer, Supplier, Sale, Purchase
from database import db
from products_window import ProductsWindow
from pos_window import POSWindow
from customers_window import CustomersWindow
from suppliers_window import SuppliersWindow
from purchase_window import PurchaseWindow
from categories_window import CategoriesWindow
from reports_window import ReportsWindow
from expenses_window import ExpensesWindow

class ShopManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المحل")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء القوائم
        self.create_menu()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # تحديث إحصائيات المخزون
        self.update_stock_alerts()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # محاولة استخدام خط عربي
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=12, weight="bold")
        except:
            # في حالة عدم توفر الخط، استخدام الخط الافتراضي
            self.arabic_font = font.Font(size=10)
            self.arabic_font_large = font.Font(size=12, weight="bold")
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="ملف", menu=file_menu, font=self.arabic_font)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_command(label="استعادة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المنتجات", menu=products_menu, font=self.arabic_font)
        products_menu.add_command(label="إدارة المنتجات", command=self.open_products_window)
        products_menu.add_command(label="إدارة التصنيفات", command=self.open_categories_window)
        products_menu.add_command(label="تقرير المخزون", command=self.show_stock_report)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المبيعات", menu=sales_menu, font=self.arabic_font)
        sales_menu.add_command(label="نقطة البيع", command=self.open_pos_window)
        sales_menu.add_command(label="تقرير المبيعات", command=self.show_sales_report)
        
        # قائمة المشتريات
        purchases_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المشتريات", menu=purchases_menu, font=self.arabic_font)
        purchases_menu.add_command(label="فاتورة شراء", command=self.open_purchase_window)
        purchases_menu.add_command(label="تقرير المشتريات", command=self.show_purchases_report)
        
        # قائمة العملاء والموردين
        contacts_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="العملاء والموردين", menu=contacts_menu, font=self.arabic_font)
        contacts_menu.add_command(label="إدارة العملاء", command=self.open_customers_window)
        contacts_menu.add_command(label="إدارة الموردين", command=self.open_suppliers_window)
        
        # قائمة المالية
        finance_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="المالية", menu=finance_menu, font=self.arabic_font)
        finance_menu.add_command(label="المصروفات والإيرادات", command=self.open_expenses_window)
        finance_menu.add_separator()
        finance_menu.add_command(label="تقرير الأرباح والخسائر", command=self.show_profit_loss_report)

        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0, font=self.arabic_font)
        menubar.add_cascade(label="التقارير", menu=reports_menu, font=self.arabic_font)
        reports_menu.add_command(label="جميع التقارير", command=self.show_reports_menu)
        reports_menu.add_separator()
        reports_menu.add_command(label="تقرير المبيعات", command=self.show_sales_report)
        reports_menu.add_command(label="تقرير المشتريات", command=self.show_purchases_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.show_stock_report)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="نظام إدارة المحل", 
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.root, bg='#f0f0f0')
        buttons_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء الأزرار الرئيسية
        self.create_main_buttons(buttons_frame)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.root, text="إحصائيات سريعة", 
                                   font=self.arabic_font, bg='#f0f0f0')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        self.create_stats_display(stats_frame)
    
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # الصف الأول من الأزرار
        row1_frame = tk.Frame(parent, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=10)
        
        # زر نقطة البيع
        pos_btn = tk.Button(row1_frame, text="نقطة البيع", font=self.arabic_font_large,
                           bg='#27ae60', fg='white', width=15, height=3,
                           command=self.open_pos_window)
        pos_btn.pack(side='left', padx=10)
        
        # زر إدارة المنتجات
        products_btn = tk.Button(row1_frame, text="إدارة المنتجات", font=self.arabic_font_large,
                                bg='#3498db', fg='white', width=15, height=3,
                                command=self.open_products_window)
        products_btn.pack(side='left', padx=10)
        
        # زر المشتريات
        purchase_btn = tk.Button(row1_frame, text="فاتورة شراء", font=self.arabic_font_large,
                                bg='#e74c3c', fg='white', width=15, height=3,
                                command=self.open_purchase_window)
        purchase_btn.pack(side='left', padx=10)
        
        # الصف الثاني من الأزرار
        row2_frame = tk.Frame(parent, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=10)
        
        # زر العملاء
        customers_btn = tk.Button(row2_frame, text="إدارة العملاء", font=self.arabic_font_large,
                                 bg='#9b59b6', fg='white', width=15, height=3,
                                 command=self.open_customers_window)
        customers_btn.pack(side='left', padx=10)
        
        # زر الموردين
        suppliers_btn = tk.Button(row2_frame, text="إدارة الموردين", font=self.arabic_font_large,
                                 bg='#f39c12', fg='white', width=15, height=3,
                                 command=self.open_suppliers_window)
        suppliers_btn.pack(side='left', padx=10)
        
        # زر التقارير
        reports_btn = tk.Button(row2_frame, text="التقارير", font=self.arabic_font_large,
                               bg='#34495e', fg='white', width=15, height=3,
                               command=self.show_reports_menu)
        reports_btn.pack(side='left', padx=10)
    
    def create_stats_display(self, parent):
        """إنشاء عرض الإحصائيات"""
        stats_inner_frame = tk.Frame(parent, bg='#f0f0f0')
        stats_inner_frame.pack(fill='x', padx=10, pady=10)
        
        # إجمالي المنتجات
        self.total_products_label = tk.Label(stats_inner_frame, text="إجمالي المنتجات: 0", 
                                           font=self.arabic_font, bg='#f0f0f0')
        self.total_products_label.pack(side='left', padx=20)
        
        # المنتجات منخفضة المخزون
        self.low_stock_label = tk.Label(stats_inner_frame, text="منتجات منخفضة المخزون: 0", 
                                      font=self.arabic_font, bg='#f0f0f0', fg='red')
        self.low_stock_label.pack(side='left', padx=20)
        
        # مبيعات اليوم
        self.today_sales_label = tk.Label(stats_inner_frame, text="مبيعات اليوم: 0", 
                                        font=self.arabic_font, bg='#f0f0f0')
        self.today_sales_label.pack(side='left', padx=20)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز", bd=1, relief='sunken', 
                                  anchor='w', font=self.arabic_font)
        self.status_bar.pack(side='bottom', fill='x')
    
    def update_stock_alerts(self):
        """تحديث تنبيهات المخزون والإحصائيات"""
        try:
            product_model = Product()
            
            # إجمالي المنتجات
            all_products = product_model.get_all_products()
            total_products = len(all_products)
            self.total_products_label.config(text=f"إجمالي المنتجات: {total_products}")
            
            # المنتجات منخفضة المخزون
            low_stock_products = product_model.get_low_stock_products()
            low_stock_count = len(low_stock_products)
            self.low_stock_label.config(text=f"منتجات منخفضة المخزون: {low_stock_count}")
            
            if low_stock_count > 0:
                self.low_stock_label.config(fg='red')
                self.status_bar.config(text=f"تحذير: {low_stock_count} منتج منخفض المخزون")
            else:
                self.low_stock_label.config(fg='green')
                self.status_bar.config(text="جاهز")
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
    
    # وظائف فتح النوافذ (ستتم إضافتها لاحقاً)
    def open_pos_window(self):
        POSWindow(self.root)
    
    def open_products_window(self):
        ProductsWindow(self.root)
    
    def open_categories_window(self):
        CategoriesWindow(self.root)
    
    def open_purchase_window(self):
        PurchaseWindow(self.root)
    
    def open_customers_window(self):
        CustomersWindow(self.root)

    def open_suppliers_window(self):
        SuppliersWindow(self.root)
    
    def show_stock_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("inventory")
        reports_window.generate_report()

    def show_sales_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("sales")

    def show_purchases_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("purchases")

    def show_profit_loss_report(self):
        reports_window = ReportsWindow(self.root)
        reports_window.report_type.set("profit_loss")

    def show_expenses_report(self):
        ReportsWindow(self.root)

    def open_expenses_window(self):
        ExpensesWindow(self.root)
    
    def show_reports_menu(self):
        ReportsWindow(self.root)
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        success, message = db.backup_database()
        if success:
            messagebox.showinfo("نجح", message)
        else:
            messagebox.showerror("خطأ", message)
    
    def restore_database(self):
        """استعادة قاعدة البيانات"""
        from tkinter import filedialog
        backup_file = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        if backup_file:
            success, message = db.restore_database(backup_file)
            if success:
                messagebox.showinfo("نجح", message)
                self.update_stock_alerts()
            else:
                messagebox.showerror("خطأ", message)

def main():
    root = tk.Tk()
    app = ShopManagementApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
