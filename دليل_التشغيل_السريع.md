# دليل التشغيل السريع - نظام إدارة المحل

## 🚀 البدء السريع

### 1. التثبيت (مرة واحدة فقط)
```bash
# تشغيل ملف تثبيت المتطلبات
install_requirements.bat

# أو يدوياً
pip install reportlab matplotlib pandas openpyxl python-bidi arabic-reshaper
```

### 2. إضافة البيانات التجريبية (اختياري)
```bash
# تشغيل ملف البيانات التجريبية
setup_sample_data.bat

# أو يدوياً
python setup_sample_data.py
```

### 3. تشغيل البرنامج
```bash
# تشغيل البرنامج
run.bat

# أو يدوياً
python main.py
```

---

## 📋 الوظائف الأساسية

### إدارة المنتجات
1. **إضافة منتج جديد**: المنتجات → إدارة المنتجات → ملء البيانات → إضافة
2. **البحث عن منتج**: استخدم خانة البحث بالاسم أو الباركود
3. **تعديل منتج**: انقر على المنتج → عدّل البيانات → تحديث
4. **تنبيه المخزون**: المنتجات منخفضة المخزون تظهر بلون أحمر

### نقطة البيع
1. **إنشاء فاتورة**: نقطة البيع → اختر العميل → أضف المنتجات → إتمام البيع
2. **إضافة منتج للفاتورة**: انقر نقراً مزدوجاً على المنتج → أدخل الكمية
3. **تطبيق خصم**: أدخل قيمة الخصم في خانة الخصم
4. **طباعة الفاتورة**: بعد إتمام البيع → طباعة الفاتورة

### المشتريات
1. **فاتورة شراء جديدة**: فاتورة شراء → اختر المورد → أضف المنتجات → إتمام الشراء
2. **تحديث المخزون**: يتم تلقائياً عند إتمام الشراء

### التقارير
1. **تقرير المبيعات**: التقارير → جميع التقارير → اختر "تقرير المبيعات" → إنشاء التقرير
2. **تقرير الأرباح**: التقارير → جميع التقارير → اختر "الأرباح والخسائر"
3. **حفظ PDF**: بعد إنشاء التقرير → حفظ PDF
4. **تصدير Excel**: بعد إنشاء التقرير → تصدير Excel

### المصروفات والإيرادات
1. **إضافة مصروف**: المالية → المصروفات والإيرادات → تبويب المصروفات → إضافة مصروف
2. **إضافة إيراد**: تبويب الإيرادات الأخرى → إضافة إيراد
3. **عرض الملخص**: تبويب الملخص → اختر الفترة → تحديث الملخص

### كشوف الحسابات
1. **كشف حساب عميل**: إدارة العملاء → اختر العميل → كشف حساب
2. **كشف حساب مورد**: إدارة الموردين → اختر المورد → كشف حساب
3. **طباعة كشف الحساب**: في نافذة كشف الحساب → طباعة

---

## ⚡ اختصارات مفيدة

### في نقطة البيع
- **نقر مزدوج على منتج**: إضافة للفاتورة
- **Delete على منتج في العربة**: حذف من الفاتورة
- **نقر يمين على منتج في العربة**: قائمة خيارات (حذف/تعديل)

### في إدارة المنتجات
- **نقر على منتج**: تحديد وعرض البيانات
- **البحث أثناء الكتابة**: البحث يتم تلقائياً

### عام
- **F5**: تحديث البيانات (في معظم النوافذ)
- **Ctrl+P**: طباعة (حيث متوفر)
- **Ctrl+S**: حفظ (حيث متوفر)

---

## 🎯 نصائح للاستخدام الأمثل

### إدارة المخزون
- ✅ حدد الحد الأدنى لكل منتج لتجنب نفاد المخزون
- ✅ راجع تقرير المخزون دورياً
- ✅ استخدم الباركود لتسريع العمليات

### المبيعات
- ✅ اختر العميل المناسب لتتبع المبيعات
- ✅ راجع الفاتورة قبل إتمام البيع
- ✅ احفظ نسخة PDF من الفواتير المهمة

### التقارير
- ✅ أنشئ تقارير دورية (يومية/أسبوعية/شهرية)
- ✅ احفظ التقارير المهمة كـ PDF
- ✅ استخدم تصدير Excel للتحليل المتقدم

### النسخ الاحتياطي
- ✅ أنشئ نسخة احتياطية يومياً
- ✅ احفظ النسخ الاحتياطية في مكان آمن
- ✅ اختبر استعادة النسخة الاحتياطية دورياً

---

## 🔧 حل المشاكل الشائعة

### "خطأ في فتح قاعدة البيانات"
**الحل**: تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

### "مكتبة غير مثبتة"
**الحل**: شغّل `install_requirements.bat` مرة أخرى

### "المنتج غير متوفر"
**الحل**: تحقق من كمية المنتج في المخزون أو أضف كمية جديدة

### "خطأ في الطباعة"
**الحل**: تأكد من تثبيت مكتبة ReportLab: `pip install reportlab`

### مشاكل النص العربي
**الحل**: تأكد من تثبيت خطوط عربية على النظام

---

## 📞 الدعم والمساعدة

### الملفات المرجعية
- `README.md` - دليل شامل للبرنامج
- `دليل_المستخدم.md` - دليل مفصل للاستخدام
- `CHANGELOG.md` - سجل التحديثات والتغييرات

### نصائح إضافية
- 📖 اقرأ دليل المستخدم للحصول على تفاصيل أكثر
- 🔄 حدّث البرنامج دورياً للحصول على أحدث الميزات
- 💾 احفظ نسخة احتياطية قبل أي تحديث مهم

---

**نصيحة**: ابدأ بالبيانات التجريبية لتتعلم كيفية استخدام البرنامج، ثم امسحها وأدخل بياناتك الحقيقية.
