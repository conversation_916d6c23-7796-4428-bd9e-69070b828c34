import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Product, Supplier, Purchase
from datetime import datetime

class PurchaseWindow:
    def __init__(self, parent):
        self.parent = parent
        self.product_model = Product()
        self.supplier_model = Supplier()
        self.purchase_model = Purchase()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("فاتورة شراء")
        self.window.geometry("1200x800")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # متغيرات الفاتورة
        self.purchase_items = []
        self.total_amount = 0.0
        self.discount = 0.0
        self.tax_rate = 0.0
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_suppliers()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة فاتورة الشراء"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#e74c3c', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="فاتورة شراء", 
                              font=self.arabic_font_large, fg='white', bg='#e74c3c')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار البحث والمنتجات (يسار)
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        self.create_search_section(left_frame)
        self.create_products_section(left_frame)
        
        # إطار الفاتورة (يمين)
        right_frame = tk.Frame(main_frame, bg='#f0f0f0', width=400)
        right_frame.pack(side='right', fill='y', padx=5)
        right_frame.pack_propagate(False)
        
        self.create_purchase_section(right_frame)
    
    def create_search_section(self, parent):
        """إنشاء قسم البحث"""
        search_frame = tk.LabelFrame(parent, text="البحث عن المنتجات", 
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        search_frame.pack(fill='x', pady=5)
        
        # إطار البحث الداخلي
        inner_frame = tk.Frame(search_frame, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(inner_frame, text="البحث:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        
        self.search_entry = tk.Entry(inner_frame, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_products)
        self.search_entry.bind('<Return>', self.search_products)
        
        search_btn = tk.Button(inner_frame, text="بحث", font=self.arabic_font,
                              bg='#3498db', fg='white', command=self.search_products)
        search_btn.pack(side='left', padx=5)
        
        # زر مسح البحث
        clear_btn = tk.Button(inner_frame, text="مسح", font=self.arabic_font,
                             bg='#95a5a6', fg='white', command=self.clear_search)
        clear_btn.pack(side='left', padx=5)
    
    def create_products_section(self, parent):
        """إنشاء قسم عرض المنتجات"""
        products_frame = tk.LabelFrame(parent, text="المنتجات", 
                                      font=self.arabic_font_bold, bg='#f0f0f0')
        products_frame.pack(fill='both', expand=True, pady=5)
        
        # جدول المنتجات
        columns = ('ID', 'الاسم', 'الباركود', 'سعر الشراء', 'الكمية الحالية', 'الوحدة')
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
        
        self.products_tree.column('ID', width=50)
        self.products_tree.column('الاسم', width=200)
        self.products_tree.column('الباركود', width=100)
        self.products_tree.column('سعر الشراء', width=100)
        self.products_tree.column('الكمية الحالية', width=100)
        self.products_tree.column('الوحدة', width=60)
        
        # شريط التمرير
        scrollbar_products = ttk.Scrollbar(products_frame, orient='vertical', 
                                          command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar_products.set)
        
        # تخطيط الجدول
        self.products_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_products.pack(side='right', fill='y')
        
        # ربط النقر المزدوج لإضافة المنتج
        self.products_tree.bind('<Double-1>', self.add_product_to_purchase)
        
        # تحميل جميع المنتجات في البداية
        self.load_all_products()
    
    def create_purchase_section(self, parent):
        """إنشاء قسم فاتورة الشراء"""
        # معلومات المورد
        supplier_frame = tk.LabelFrame(parent, text="معلومات المورد", 
                                      font=self.arabic_font_bold, bg='#f0f0f0')
        supplier_frame.pack(fill='x', pady=5)
        
        tk.Label(supplier_frame, text="المورد:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        
        self.supplier_combo = ttk.Combobox(supplier_frame, font=self.arabic_font, 
                                          width=25, state='readonly')
        self.supplier_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # عناصر الشراء
        items_frame = tk.LabelFrame(parent, text="عناصر الشراء", 
                                   font=self.arabic_font_bold, bg='#f0f0f0')
        items_frame.pack(fill='both', expand=True, pady=5)
        
        # جدول عناصر الشراء
        purchase_columns = ('المنتج', 'الكمية', 'سعر الوحدة', 'الإجمالي')
        self.purchase_tree = ttk.Treeview(items_frame, columns=purchase_columns, show='headings', height=10)
        
        for col in purchase_columns:
            self.purchase_tree.heading(col, text=col)
        
        self.purchase_tree.column('المنتج', width=150)
        self.purchase_tree.column('الكمية', width=60)
        self.purchase_tree.column('سعر الوحدة', width=80)
        self.purchase_tree.column('الإجمالي', width=80)
        
        # شريط التمرير للعناصر
        scrollbar_purchase = ttk.Scrollbar(items_frame, orient='vertical', 
                                          command=self.purchase_tree.yview)
        self.purchase_tree.configure(yscrollcommand=scrollbar_purchase.set)
        
        self.purchase_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_purchase.pack(side='right', fill='y')
        
        # ربط النقر لحذف عنصر
        self.purchase_tree.bind('<Delete>', self.remove_from_purchase)
        self.purchase_tree.bind('<Button-3>', self.show_purchase_context_menu)
        
        # إطار الحسابات
        calc_frame = tk.LabelFrame(parent, text="الحسابات", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        calc_frame.pack(fill='x', pady=5)
        
        # الإجمالي الفرعي
        tk.Label(calc_frame, text="الإجمالي الفرعي:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=2)
        self.subtotal_label = tk.Label(calc_frame, text="0.00", font=self.arabic_font_bold, 
                                      bg='#f0f0f0', fg='blue')
        self.subtotal_label.grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        # الخصم
        tk.Label(calc_frame, text="الخصم:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='e', padx=5, pady=2)
        self.discount_entry = tk.Entry(calc_frame, font=self.arabic_font, width=10)
        self.discount_entry.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        self.discount_entry.bind('<KeyRelease>', self.calculate_total)
        self.discount_entry.insert(0, "0")
        
        # الضريبة
        tk.Label(calc_frame, text="الضريبة %:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=2, column=0, sticky='e', padx=5, pady=2)
        self.tax_entry = tk.Entry(calc_frame, font=self.arabic_font, width=10)
        self.tax_entry.grid(row=2, column=1, sticky='w', padx=5, pady=2)
        self.tax_entry.bind('<KeyRelease>', self.calculate_total)
        self.tax_entry.insert(0, "0")
        
        # الإجمالي النهائي
        tk.Label(calc_frame, text="الإجمالي النهائي:", font=self.arabic_font_bold, bg='#f0f0f0').grid(
            row=3, column=0, sticky='e', padx=5, pady=5)
        self.total_label = tk.Label(calc_frame, text="0.00", font=self.arabic_font_large, 
                                   bg='#f0f0f0', fg='red')
        self.total_label.grid(row=3, column=1, sticky='w', padx=5, pady=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.pack(fill='x', pady=10)
        
        # زر إتمام الشراء
        complete_btn = tk.Button(buttons_frame, text="إتمام الشراء", font=self.arabic_font_bold,
                                bg='#e74c3c', fg='white', height=2,
                                command=self.complete_purchase)
        complete_btn.pack(fill='x', pady=2)
        
        # زر مسح الفاتورة
        clear_btn = tk.Button(buttons_frame, text="مسح الفاتورة", font=self.arabic_font,
                             bg='#95a5a6', fg='white',
                             command=self.clear_purchase)
        clear_btn.pack(fill='x', pady=2)
        
        # زر طباعة الفاتورة
        print_btn = tk.Button(buttons_frame, text="طباعة الفاتورة", font=self.arabic_font,
                             bg='#3498db', fg='white',
                             command=self.print_purchase)
        print_btn.pack(fill='x', pady=2)
    
    def load_suppliers(self):
        """تحميل الموردين"""
        suppliers = self.supplier_model.get_all_suppliers()
        supplier_names = [supplier[1] for supplier in suppliers]
        self.supplier_combo['values'] = supplier_names
        
        # حفظ معرفات الموردين
        self.suppliers_dict = {}
        for supplier in suppliers:
            self.suppliers_dict[supplier[1]] = supplier[0]
    
    def load_all_products(self):
        """تحميل جميع المنتجات"""
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تحميل المنتجات
        products = self.product_model.get_all_products()
        for product in products:
            self.products_tree.insert('', 'end', values=(
                product[0],  # ID
                product[1],  # name
                product[2] or '',  # barcode
                f"{product[4]:.2f}",  # purchase_price
                product[6],  # quantity
                product[8]   # unit
            ))
    
    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_entry.get().strip()
        
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        if search_term:
            products = self.product_model.search_products(search_term)
        else:
            products = self.product_model.get_all_products()
        
        # عرض النتائج
        for product in products:
            self.products_tree.insert('', 'end', values=(
                product[0],  # ID
                product[1],  # name
                product[2] or '',  # barcode
                f"{product[4]:.2f}",  # purchase_price
                product[6],  # quantity
                product[8]   # unit
            ))
    
    def clear_search(self):
        """مسح البحث"""
        self.search_entry.delete(0, 'end')
        self.load_all_products()
    
    def add_product_to_purchase(self, event=None):
        """إضافة منتج إلى فاتورة الشراء"""
        selection = self.products_tree.selection()
        if not selection:
            return
        
        item = self.products_tree.item(selection[0])
        product_data = item['values']
        
        product_id = product_data[0]
        product_name = product_data[1]
        current_purchase_price = float(product_data[3])
        
        # نافذة إدخال تفاصيل الشراء
        purchase_window = tk.Toplevel(self.window)
        purchase_window.title("تفاصيل الشراء")
        purchase_window.geometry("350x200")
        purchase_window.configure(bg='#f0f0f0')
        purchase_window.transient(self.window)
        purchase_window.grab_set()
        
        tk.Label(purchase_window, text=f"المنتج: {product_name}", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=10)
        
        # الكمية
        tk.Label(purchase_window, text="الكمية:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        quantity_entry = tk.Entry(purchase_window, font=self.arabic_font, width=15)
        quantity_entry.pack(pady=5)
        quantity_entry.insert(0, "1")
        quantity_entry.focus()
        quantity_entry.select_range(0, 'end')
        
        # سعر الوحدة
        tk.Label(purchase_window, text="سعر الوحدة:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        price_entry = tk.Entry(purchase_window, font=self.arabic_font, width=15)
        price_entry.pack(pady=5)
        price_entry.insert(0, str(current_purchase_price))
        
        def add_to_purchase():
            try:
                quantity = int(quantity_entry.get())
                unit_price = float(price_entry.get())
                
                if quantity <= 0:
                    messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                    return
                
                if unit_price <= 0:
                    messagebox.showerror("خطأ", "السعر يجب أن يكون أكبر من صفر")
                    return
                
                # التحقق من وجود المنتج في الفاتورة
                existing_item = None
                for purchase_item in self.purchase_items:
                    if purchase_item['product_id'] == product_id:
                        existing_item = purchase_item
                        break
                
                if existing_item:
                    # زيادة الكمية
                    existing_item['quantity'] += quantity
                    existing_item['unit_price'] = unit_price  # تحديث السعر
                    existing_item['total_price'] = existing_item['quantity'] * unit_price
                else:
                    # إضافة منتج جديد
                    purchase_item = {
                        'product_id': product_id,
                        'product_name': product_name,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_price': quantity * unit_price
                    }
                    self.purchase_items.append(purchase_item)
                
                self.update_purchase_display()
                self.calculate_total()
                purchase_window.destroy()
                
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال أرقام صحيحة")
        
        btn_frame = tk.Frame(purchase_window, bg='#f0f0f0')
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="إضافة", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=add_to_purchase).pack(side='left', padx=5)
        tk.Button(btn_frame, text="إلغاء", font=self.arabic_font,
                 bg='#e74c3c', fg='white', command=purchase_window.destroy).pack(side='left', padx=5)
        
        # ربط Enter بإضافة المنتج
        quantity_entry.bind('<Return>', lambda e: price_entry.focus())
        price_entry.bind('<Return>', lambda e: add_to_purchase())
    
    def update_purchase_display(self):
        """تحديث عرض فاتورة الشراء"""
        # مسح الفاتورة
        for item in self.purchase_tree.get_children():
            self.purchase_tree.delete(item)
        
        # إضافة العناصر
        for purchase_item in self.purchase_items:
            self.purchase_tree.insert('', 'end', values=(
                purchase_item['product_name'],
                purchase_item['quantity'],
                f"{purchase_item['unit_price']:.2f}",
                f"{purchase_item['total_price']:.2f}"
            ))
    
    def calculate_total(self, event=None):
        """حساب الإجمالي"""
        # حساب الإجمالي الفرعي
        subtotal = sum(item['total_price'] for item in self.purchase_items)
        self.subtotal_label.config(text=f"{subtotal:.2f}")
        
        # حساب الخصم
        try:
            discount = float(self.discount_entry.get() or 0)
        except ValueError:
            discount = 0
        
        # حساب الضريبة
        try:
            tax_rate = float(self.tax_entry.get() or 0)
        except ValueError:
            tax_rate = 0
        
        # حساب الإجمالي النهائي
        after_discount = subtotal - discount
        tax_amount = after_discount * (tax_rate / 100)
        final_total = after_discount + tax_amount
        
        self.total_label.config(text=f"{final_total:.2f}")
        
        # حفظ القيم
        self.total_amount = subtotal
        self.discount = discount
        self.tax_rate = tax_rate
    
    def remove_from_purchase(self, event=None):
        """حذف عنصر من فاتورة الشراء"""
        selection = self.purchase_tree.selection()
        if selection:
            item_index = self.purchase_tree.index(selection[0])
            del self.purchase_items[item_index]
            self.update_purchase_display()
            self.calculate_total()
    
    def show_purchase_context_menu(self, event):
        """عرض قائمة السياق لفاتورة الشراء"""
        selection = self.purchase_tree.selection()
        if selection:
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="حذف", command=self.remove_from_purchase)
            context_menu.add_command(label="تعديل", command=self.edit_purchase_item)
            context_menu.tk_popup(event.x_root, event.y_root)
    
    def edit_purchase_item(self):
        """تعديل عنصر في فاتورة الشراء"""
        selection = self.purchase_tree.selection()
        if not selection:
            return
        
        item_index = self.purchase_tree.index(selection[0])
        purchase_item = self.purchase_items[item_index]
        
        # نافذة تعديل العنصر
        edit_window = tk.Toplevel(self.window)
        edit_window.title("تعديل العنصر")
        edit_window.geometry("350x200")
        edit_window.configure(bg='#f0f0f0')
        edit_window.transient(self.window)
        edit_window.grab_set()
        
        tk.Label(edit_window, text=f"المنتج: {purchase_item['product_name']}", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=10)
        
        # الكمية
        tk.Label(edit_window, text="الكمية:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        quantity_entry = tk.Entry(edit_window, font=self.arabic_font, width=15)
        quantity_entry.pack(pady=5)
        quantity_entry.insert(0, str(purchase_item['quantity']))
        quantity_entry.focus()
        quantity_entry.select_range(0, 'end')
        
        # سعر الوحدة
        tk.Label(edit_window, text="سعر الوحدة:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        price_entry = tk.Entry(edit_window, font=self.arabic_font, width=15)
        price_entry.pack(pady=5)
        price_entry.insert(0, str(purchase_item['unit_price']))
        
        def update_item():
            try:
                new_quantity = int(quantity_entry.get())
                new_price = float(price_entry.get())
                
                if new_quantity <= 0:
                    # حذف العنصر إذا كانت الكمية صفر أو أقل
                    del self.purchase_items[item_index]
                else:
                    purchase_item['quantity'] = new_quantity
                    purchase_item['unit_price'] = new_price
                    purchase_item['total_price'] = new_quantity * new_price
                
                self.update_purchase_display()
                self.calculate_total()
                edit_window.destroy()
                
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال أرقام صحيحة")
        
        btn_frame = tk.Frame(edit_window, bg='#f0f0f0')
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', command=update_item).pack(side='left', padx=5)
        tk.Button(btn_frame, text="إلغاء", font=self.arabic_font,
                 bg='#e74c3c', fg='white', command=edit_window.destroy).pack(side='left', padx=5)
        
        quantity_entry.bind('<Return>', lambda e: price_entry.focus())
        price_entry.bind('<Return>', lambda e: update_item())
    
    def clear_purchase(self):
        """مسح فاتورة الشراء"""
        if self.purchase_items:
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح الفاتورة؟"):
                self.purchase_items.clear()
                self.update_purchase_display()
                self.calculate_total()
    
    def complete_purchase(self):
        """إتمام الشراء"""
        if not self.purchase_items:
            messagebox.showerror("خطأ", "الفاتورة فارغة")
            return
        
        if not self.supplier_combo.get():
            messagebox.showerror("خطأ", "يجب اختيار مورد")
            return
        
        # الحصول على معرف المورد
        supplier_name = self.supplier_combo.get()
        supplier_id = self.suppliers_dict.get(supplier_name)
        
        # تحضير بيانات العناصر
        items = []
        for purchase_item in self.purchase_items:
            items.append({
                'product_id': purchase_item['product_id'],
                'quantity': purchase_item['quantity'],
                'unit_price': purchase_item['unit_price']
            })
        
        # إنشاء فاتورة الشراء
        success, purchase_id, invoice_number, message = self.purchase_model.create_purchase(
            supplier_id, items, self.discount, self.tax_rate, 'نقدي', ''
        )
        
        if success:
            messagebox.showinfo("نجح", f"تم إنشاء فاتورة الشراء بنجاح\nرقم الفاتورة: {invoice_number}")
            
            # حفظ معرف الفاتورة للطباعة
            self.last_purchase_id = purchase_id
            
            # مسح الفاتورة
            self.purchase_items.clear()
            self.update_purchase_display()
            self.calculate_total()
            
            # تحديث قائمة المنتجات
            self.load_all_products()
            
            # عرض خيار الطباعة
            if messagebox.askyesno("طباعة", "هل تريد طباعة الفاتورة؟"):
                self.print_purchase()
        else:
            messagebox.showerror("خطأ", message)
    
    def print_purchase(self):
        """طباعة فاتورة الشراء"""
        if hasattr(self, 'last_purchase_id'):
            try:
                from pdf_generator import pdf_generator
                from tkinter import filedialog

                # اختيار مكان الحفظ
                filename = filedialog.asksaveasfilename(
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    title="حفظ فاتورة الشراء"
                )

                if filename:
                    # إنشاء ملف PDF
                    saved_file = pdf_generator.generate_purchase_invoice_pdf(self.last_purchase_id, filename)
                    messagebox.showinfo("نجح", f"تم حفظ الفاتورة في:\n{saved_file}")

                    # فتح الملف
                    import os
                    os.startfile(saved_file)

            except ImportError:
                messagebox.showerror("خطأ", "مكتبة ReportLab غير مثبتة\nقم بتثبيتها باستخدام: pip install reportlab")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء الفاتورة: {str(e)}")
        else:
            messagebox.showerror("خطأ", "لا توجد فاتورة للطباعة")
