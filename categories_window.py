import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Category

class CategoriesWindow:
    def __init__(self, parent):
        self.parent = parent
        self.category_model = Category()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة التصنيفات")
        self.window.geometry("700x500")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_categories()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=10, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة إدارة التصنيفات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#8e44ad', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة التصنيفات", 
                              font=self.arabic_font_bold, fg='white', bg='#8e44ad')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار النموذج (أعلى)
        form_frame = tk.LabelFrame(main_frame, text="بيانات التصنيف", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        form_frame.pack(fill='x', padx=5, pady=5)
        
        self.create_category_form(form_frame)
        
        # إطار القائمة (أسفل)
        list_frame = tk.LabelFrame(main_frame, text="قائمة التصنيفات", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.create_categories_list(list_frame)
    
    def create_category_form(self, parent):
        """إنشاء نموذج إدخال التصنيف"""
        # إطار داخلي للنموذج
        inner_frame = tk.Frame(parent, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # اسم التصنيف
        tk.Label(inner_frame, text="اسم التصنيف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        self.name_entry = tk.Entry(inner_frame, font=self.arabic_font, width=30)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # الوصف
        tk.Label(inner_frame, text="الوصف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='ne', padx=5, pady=5)
        self.description_text = tk.Text(inner_frame, font=self.arabic_font, width=30, height=3)
        self.description_text.grid(row=1, column=1, padx=5, pady=5)
        
        # الأزرار
        buttons_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        buttons_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.add_btn = tk.Button(buttons_frame, text="إضافة", font=self.arabic_font,
                                bg='#27ae60', fg='white', width=10,
                                command=self.add_category)
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = tk.Button(buttons_frame, text="تحديث", font=self.arabic_font,
                                   bg='#3498db', fg='white', width=10,
                                   command=self.update_category, state='disabled')
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = tk.Button(buttons_frame, text="حذف", font=self.arabic_font,
                                   bg='#e74c3c', fg='white', width=10,
                                   command=self.delete_category, state='disabled')
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = tk.Button(buttons_frame, text="مسح", font=self.arabic_font,
                                  bg='#95a5a6', fg='white', width=10,
                                  command=self.clear_form)
        self.clear_btn.pack(side='left', padx=5)
    
    def create_categories_list(self, parent):
        """إنشاء قائمة التصنيفات"""
        # جدول التصنيفات
        columns = ('ID', 'الاسم', 'الوصف', 'تاريخ الإنشاء')
        self.categories_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        self.categories_tree.heading('ID', text='ID')
        self.categories_tree.heading('الاسم', text='الاسم')
        self.categories_tree.heading('الوصف', text='الوصف')
        self.categories_tree.heading('تاريخ الإنشاء', text='تاريخ الإنشاء')
        
        # تحديد عرض الأعمدة
        self.categories_tree.column('ID', width=50)
        self.categories_tree.column('الاسم', width=150)
        self.categories_tree.column('الوصف', width=300)
        self.categories_tree.column('تاريخ الإنشاء', width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.categories_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # ربط حدث النقر على التصنيف
        self.categories_tree.bind('<ButtonRelease-1>', self.on_category_select)
        
        # متغير لحفظ ID التصنيف المحدد
        self.selected_category_id = None
    
    def load_categories(self):
        """تحميل التصنيفات"""
        # مسح الجدول
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)
        
        # تحميل التصنيفات
        categories = self.category_model.get_all_categories()
        for category in categories:
            self.categories_tree.insert('', 'end', values=(
                category[0],  # ID
                category[1],  # name
                category[2] or '',  # description
                category[3]   # created_at
            ))
    
    def on_category_select(self, event):
        """عند تحديد تصنيف من القائمة"""
        selection = self.categories_tree.selection()
        if selection:
            item = self.categories_tree.item(selection[0])
            category_data = item['values']
            
            self.selected_category_id = category_data[0]
            
            # ملء النموذج
            self.clear_form()
            self.name_entry.insert(0, category_data[1])
            if category_data[2]:
                self.description_text.insert('1.0', category_data[2])
            
            # تفعيل أزرار التحديث والحذف
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_entry.delete(0, 'end')
        self.description_text.delete('1.0', 'end')
        
        self.selected_category_id = None
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم التصنيف")
            return False
        return True
    
    def add_category(self):
        """إضافة تصنيف جديد"""
        if not self.validate_form():
            return
        
        # جمع البيانات
        name = self.name_entry.get().strip()
        description = self.description_text.get('1.0', 'end').strip()
        
        # إضافة التصنيف
        success, category_id, message = self.category_model.add_category(name, description)
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_categories()
        else:
            messagebox.showerror("خطأ", message)
    
    def update_category(self):
        """تحديث التصنيف"""
        if not self.selected_category_id:
            messagebox.showerror("خطأ", "يجب تحديد تصنيف للتحديث")
            return
        
        if not self.validate_form():
            return
        
        messagebox.showinfo("قريباً", "ميزة تحديث التصنيف قيد التطوير")
    
    def delete_category(self):
        """حذف التصنيف"""
        if not self.selected_category_id:
            messagebox.showerror("خطأ", "يجب تحديد تصنيف للحذف")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا التصنيف؟\nسيتم حذف جميع المنتجات المرتبطة به."):
            messagebox.showinfo("قريباً", "ميزة حذف التصنيف قيد التطوير")
