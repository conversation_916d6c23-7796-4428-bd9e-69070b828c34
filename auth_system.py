#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام المصادقة والصلاحيات
يدير تسجيل الدخول والخروج وصلاحيات المستخدمين
"""

import hashlib
import sqlite3
from datetime import datetime
from database import db

class AuthSystem:
    def __init__(self):
        self.db = db
        self.current_user = None
        self.setup_default_admin()
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def setup_default_admin(self):
        """إنشاء مستخدم المدير الافتراضي"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من وجود مستخدم مدير
            cursor.execute('SELECT COUNT(*) FROM users WHERE role = "admin"')
            admin_count = cursor.fetchone()[0]
            
            if admin_count == 0:
                # إنشاء مستخدم مدير افتراضي
                hashed_password = self.hash_password("admin123")
                cursor.execute('''
                    INSERT INTO users (username, password, full_name, role, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("admin", hashed_password, "المدير العام", "admin", 1))
                
                conn.commit()
                print("تم إنشاء مستخدم المدير الافتراضي:")
                print("اسم المستخدم: admin")
                print("كلمة المرور: admin123")
            
            conn.close()
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم الافتراضي: {str(e)}")
    
    def login(self, username, password):
        """تسجيل الدخول"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            hashed_password = self.hash_password(password)
            
            cursor.execute('''
                SELECT id, username, full_name, role, is_active, last_login
                FROM users 
                WHERE username = ? AND password = ? AND is_active = 1
            ''', (username, hashed_password))
            
            user = cursor.fetchone()
            
            if user:
                # تحديث آخر تسجيل دخول
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (user[0],))
                
                # تسجيل عملية الدخول
                cursor.execute('''
                    INSERT INTO user_sessions (user_id, login_time, ip_address)
                    VALUES (?, CURRENT_TIMESTAMP, ?)
                ''', (user[0], "127.0.0.1"))
                
                conn.commit()
                
                # حفظ بيانات المستخدم الحالي
                self.current_user = {
                    'id': user[0],
                    'username': user[1],
                    'full_name': user[2],
                    'role': user[3],
                    'is_active': user[4],
                    'last_login': user[5]
                }
                
                conn.close()
                return True, "تم تسجيل الدخول بنجاح"
            else:
                conn.close()
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة"
                
        except Exception as e:
            return False, f"خطأ في تسجيل الدخول: {str(e)}"
    
    def logout(self):
        """تسجيل الخروج"""
        if self.current_user:
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                
                # تحديث وقت الخروج
                cursor.execute('''
                    UPDATE user_sessions 
                    SET logout_time = CURRENT_TIMESTAMP 
                    WHERE user_id = ? AND logout_time IS NULL
                    ORDER BY login_time DESC LIMIT 1
                ''', (self.current_user['id'],))
                
                conn.commit()
                conn.close()
                
                self.current_user = None
                return True, "تم تسجيل الخروج بنجاح"
            except Exception as e:
                return False, f"خطأ في تسجيل الخروج: {str(e)}"
        
        return True, "لم يتم تسجيل الدخول"
    
    def is_logged_in(self):
        """التحقق من تسجيل الدخول"""
        return self.current_user is not None
    
    def has_permission(self, permission):
        """التحقق من الصلاحية"""
        if not self.current_user:
            return False
        
        role = self.current_user['role']
        
        # صلاحيات المدير
        if role == 'admin':
            return True
        
        # صلاحيات المدير المساعد
        elif role == 'manager':
            restricted_permissions = ['delete_user', 'manage_users', 'backup_restore']
            return permission not in restricted_permissions
        
        # صلاحيات الكاشير
        elif role == 'cashier':
            allowed_permissions = [
                'view_products', 'pos_sales', 'view_customers', 
                'add_customer', 'view_sales_reports'
            ]
            return permission in allowed_permissions
        
        # صلاحيات المخزن
        elif role == 'inventory':
            allowed_permissions = [
                'view_products', 'add_product', 'edit_product', 
                'view_inventory', 'manage_purchases', 'view_suppliers'
            ]
            return permission in allowed_permissions
        
        return False
    
    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        return self.current_user
    
    def add_user(self, username, password, full_name, role, is_active=True):
        """إضافة مستخدم جديد"""
        if not self.has_permission('manage_users'):
            return False, "ليس لديك صلاحية لإضافة المستخدمين"
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من عدم وجود اسم المستخدم
            cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', (username,))
            if cursor.fetchone()[0] > 0:
                conn.close()
                return False, "اسم المستخدم موجود مسبقاً"
            
            # إضافة المستخدم
            hashed_password = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, hashed_password, full_name, role, is_active, self.current_user['id']))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"تم إضافة المستخدم بنجاح (ID: {user_id})"
            
        except Exception as e:
            return False, f"خطأ في إضافة المستخدم: {str(e)}"
    
    def update_user(self, user_id, username=None, full_name=None, role=None, is_active=None):
        """تحديث بيانات المستخدم"""
        if not self.has_permission('manage_users'):
            return False, "ليس لديك صلاحية لتحديث المستخدمين"
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # بناء استعلام التحديث
            updates = []
            params = []
            
            if username:
                updates.append("username = ?")
                params.append(username)
            if full_name:
                updates.append("full_name = ?")
                params.append(full_name)
            if role:
                updates.append("role = ?")
                params.append(role)
            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)
            
            if not updates:
                return False, "لا توجد بيانات للتحديث"
            
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(user_id)
            
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "تم تحديث المستخدم بنجاح"
            else:
                conn.close()
                return False, "المستخدم غير موجود"
                
        except Exception as e:
            return False, f"خطأ في تحديث المستخدم: {str(e)}"
    
    def change_password(self, user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من كلمة المرور القديمة
            old_hashed = self.hash_password(old_password)
            cursor.execute('SELECT id FROM users WHERE id = ? AND password = ?', 
                          (user_id, old_hashed))
            
            if not cursor.fetchone():
                conn.close()
                return False, "كلمة المرور القديمة غير صحيحة"
            
            # تحديث كلمة المرور
            new_hashed = self.hash_password(new_password)
            cursor.execute('''
                UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (new_hashed, user_id))
            
            conn.commit()
            conn.close()
            
            return True, "تم تغيير كلمة المرور بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        if not self.has_permission('view_users'):
            return []
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, full_name, role, is_active, 
                       created_at, last_login
                FROM users 
                ORDER BY created_at DESC
            ''')
            
            users = cursor.fetchall()
            conn.close()
            
            return users
            
        except Exception as e:
            print(f"خطأ في جلب المستخدمين: {str(e)}")
            return []
    
    def get_user_sessions(self, user_id=None):
        """الحصول على جلسات المستخدمين"""
        if not self.has_permission('view_users'):
            return []
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            if user_id:
                cursor.execute('''
                    SELECT us.*, u.username, u.full_name
                    FROM user_sessions us
                    JOIN users u ON us.user_id = u.id
                    WHERE us.user_id = ?
                    ORDER BY us.login_time DESC
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT us.*, u.username, u.full_name
                    FROM user_sessions us
                    JOIN users u ON us.user_id = u.id
                    ORDER BY us.login_time DESC
                    LIMIT 100
                ''')
            
            sessions = cursor.fetchall()
            conn.close()
            
            return sessions
            
        except Exception as e:
            print(f"خطأ في جلب الجلسات: {str(e)}")
            return []
    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        if not self.has_permission('delete_user'):
            return False, "ليس لديك صلاحية لحذف المستخدمين"
        
        if user_id == self.current_user['id']:
            return False, "لا يمكن حذف المستخدم الحالي"
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من وجود المستخدم
            cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            
            if not user:
                conn.close()
                return False, "المستخدم غير موجود"
            
            # منع حذف المدير الوحيد
            if user[0] == 'admin':
                cursor.execute('SELECT COUNT(*) FROM users WHERE role = "admin" AND is_active = 1')
                admin_count = cursor.fetchone()[0]
                if admin_count <= 1:
                    conn.close()
                    return False, "لا يمكن حذف المدير الوحيد"
            
            # حذف المستخدم (تعطيل بدلاً من الحذف)
            cursor.execute('''
                UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user_id,))
            
            conn.commit()
            conn.close()
            
            return True, "تم تعطيل المستخدم بنجاح"
            
        except Exception as e:
            return False, f"خطأ في حذف المستخدم: {str(e)}"

# إنشاء مثيل من نظام المصادقة
auth_system = AuthSystem()
