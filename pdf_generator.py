#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
مولد ملفات PDF للفواتير والتقارير
يستخدم مكتبة ReportLab لإنشاء ملفات PDF باللغة العربية
"""

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

import os
from datetime import datetime
from database import db

class PDFGenerator:
    def __init__(self):
        self.db = db
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        if not REPORTLAB_AVAILABLE:
            return
        
        try:
            # محاولة تسجيل خط عربي
            # يمكن تحميل خط عربي مثل Amiri أو Noto Sans Arabic
            # وضعه في مجلد fonts في المشروع
            font_path = os.path.join(os.path.dirname(__file__), 'fonts', 'NotoSansArabic-Regular.ttf')
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                # استخدام خط افتراضي
                self.arabic_font = 'Helvetica'
        except:
            self.arabic_font = 'Helvetica'
    
    def check_reportlab(self):
        """التحقق من توفر مكتبة ReportLab"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة ReportLab غير مثبتة. قم بتثبيتها باستخدام: pip install reportlab")
        return True
    
    def generate_sale_invoice_pdf(self, sale_id, filename=None):
        """إنشاء فاتورة مبيعات PDF"""
        self.check_reportlab()
        
        # جلب بيانات الفاتورة
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # بيانات الفاتورة الأساسية
        cursor.execute('''
            SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE s.id = ?
        ''', (sale_id,))
        sale = cursor.fetchone()
        
        if not sale:
            raise ValueError("الفاتورة غير موجودة")
        
        # تفاصيل الفاتورة
        cursor.execute('''
            SELECT si.*, p.name as product_name, p.unit
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
        ''', (sale_id,))
        items = cursor.fetchall()
        
        conn.close()
        
        # إنشاء اسم الملف إذا لم يتم تحديده
        if not filename:
            filename = f"فاتورة_مبيعات_{sale[1]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                               topMargin=2*cm, bottomMargin=2*cm)
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        
        # نمط العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=30
        )
        
        # نمط النص العادي
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_RIGHT
        )
        
        # نمط النص المتوسط
        center_style = ParagraphStyle(
            'CustomCenter',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_CENTER
        )
        
        # بناء محتوى المستند
        story = []
        
        # العنوان
        story.append(Paragraph("فاتورة مبيعات", title_style))
        story.append(Spacer(1, 20))
        
        # معلومات الفاتورة
        invoice_info = f"""
        <b>رقم الفاتورة:</b> {sale[1]}<br/>
        <b>التاريخ:</b> {sale[10][:10]}<br/>
        <b>العميل:</b> {sale[11] or 'عميل نقدي'}<br/>
        """
        
        if sale[12]:  # customer_phone
            invoice_info += f"<b>الهاتف:</b> {sale[12]}<br/>"
        
        story.append(Paragraph(invoice_info, normal_style))
        story.append(Spacer(1, 20))
        
        # جدول المنتجات
        table_data = [['الإجمالي', 'سعر الوحدة', 'الكمية', 'الوحدة', 'اسم المنتج']]
        
        for item in items:
            table_data.append([
                f"{item[5]:.2f}",  # total_price
                f"{item[4]:.2f}",  # unit_price
                str(item[3]),      # quantity
                item[7],           # unit
                item[6]            # product_name
            ])
        
        # إنشاء الجدول
        table = Table(table_data, colWidths=[3*cm, 3*cm, 2*cm, 2*cm, 6*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # الإجماليات
        totals_info = f"""
        <b>الإجمالي الفرعي:</b> {sale[3]:.2f} جنيه<br/>
        <b>الخصم:</b> {sale[4]:.2f} جنيه<br/>
        <b>الضريبة ({sale[5]:.1f}%):</b> {sale[6]:.2f} جنيه<br/>
        <b>الإجمالي النهائي:</b> {sale[7]:.2f} جنيه<br/>
        <b>طريقة الدفع:</b> {sale[8]}
        """
        
        story.append(Paragraph(totals_info, normal_style))
        story.append(Spacer(1, 30))
        
        # ملاحظات
        if sale[9]:  # notes
            story.append(Paragraph(f"<b>ملاحظات:</b> {sale[9]}", normal_style))
            story.append(Spacer(1, 20))
        
        # تذييل
        footer = "شكراً لتعاملكم معنا"
        story.append(Paragraph(footer, center_style))
        
        # بناء المستند
        doc.build(story)
        
        return filename
    
    def generate_purchase_invoice_pdf(self, purchase_id, filename=None):
        """إنشاء فاتورة مشتريات PDF"""
        self.check_reportlab()
        
        # جلب بيانات الفاتورة
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # بيانات الفاتورة الأساسية
        cursor.execute('''
            SELECT p.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.id = ?
        ''', (purchase_id,))
        purchase = cursor.fetchone()
        
        if not purchase:
            raise ValueError("فاتورة الشراء غير موجودة")
        
        # تفاصيل الفاتورة
        cursor.execute('''
            SELECT pi.*, p.name as product_name, p.unit
            FROM purchase_items pi
            JOIN products p ON pi.product_id = p.id
            WHERE pi.purchase_id = ?
        ''', (purchase_id,))
        items = cursor.fetchall()
        
        conn.close()
        
        # إنشاء اسم الملف إذا لم يتم تحديده
        if not filename:
            filename = f"فاتورة_شراء_{purchase[1]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # إنشاء المستند (نفس الطريقة مع تغيير العنوان والبيانات)
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                               topMargin=2*cm, bottomMargin=2*cm)
        
        # إعداد الأنماط (نفس الأنماط السابقة)
        styles = getSampleStyleSheet()
        
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=30
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_RIGHT
        )
        
        center_style = ParagraphStyle(
            'CustomCenter',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_CENTER
        )
        
        # بناء محتوى المستند
        story = []
        
        # العنوان
        story.append(Paragraph("فاتورة شراء", title_style))
        story.append(Spacer(1, 20))
        
        # معلومات الفاتورة
        invoice_info = f"""
        <b>رقم الفاتورة:</b> {purchase[1]}<br/>
        <b>التاريخ:</b> {purchase[10][:10]}<br/>
        <b>المورد:</b> {purchase[11] or 'غير محدد'}<br/>
        """
        
        if purchase[12]:  # supplier_phone
            invoice_info += f"<b>الهاتف:</b> {purchase[12]}<br/>"
        
        story.append(Paragraph(invoice_info, normal_style))
        story.append(Spacer(1, 20))
        
        # جدول المنتجات
        table_data = [['الإجمالي', 'سعر الوحدة', 'الكمية', 'الوحدة', 'اسم المنتج']]
        
        for item in items:
            table_data.append([
                f"{item[5]:.2f}",  # total_price
                f"{item[4]:.2f}",  # unit_price
                str(item[3]),      # quantity
                item[7],           # unit
                item[6]            # product_name
            ])
        
        # إنشاء الجدول
        table = Table(table_data, colWidths=[3*cm, 3*cm, 2*cm, 2*cm, 6*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # الإجماليات
        totals_info = f"""
        <b>الإجمالي الفرعي:</b> {purchase[3]:.2f} جنيه<br/>
        <b>الخصم:</b> {purchase[4]:.2f} جنيه<br/>
        <b>الضريبة ({purchase[5]:.1f}%):</b> {purchase[6]:.2f} جنيه<br/>
        <b>الإجمالي النهائي:</b> {purchase[7]:.2f} جنيه<br/>
        <b>طريقة الدفع:</b> {purchase[8]}
        """
        
        story.append(Paragraph(totals_info, normal_style))
        story.append(Spacer(1, 30))
        
        # ملاحظات
        if purchase[9]:  # notes
            story.append(Paragraph(f"<b>ملاحظات:</b> {purchase[9]}", normal_style))
            story.append(Spacer(1, 20))
        
        # تذييل
        footer = "فاتورة شراء - نظام إدارة المحل"
        story.append(Paragraph(footer, center_style))
        
        # بناء المستند
        doc.build(story)
        
        return filename
    
    def generate_report_pdf(self, report_data, filename=None):
        """إنشاء تقرير PDF"""
        self.check_reportlab()
        
        if not filename:
            filename = f"تقرير_{report_data['type']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                               topMargin=2*cm, bottomMargin=2*cm)
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=30
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_RIGHT
        )
        
        # بناء محتوى المستند
        story = []
        
        # العنوان
        report_titles = {
            'sales': 'تقرير المبيعات',
            'purchases': 'تقرير المشتريات',
            'profit_loss': 'تقرير الأرباح والخسائر',
            'inventory': 'تقرير المخزون',
            'customers': 'تقرير العملاء',
            'suppliers': 'تقرير الموردين'
        }
        
        title = report_titles.get(report_data['type'], 'تقرير')
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 20))
        
        # الفترة
        story.append(Paragraph(f"الفترة: {report_data['period']}", normal_style))
        story.append(Spacer(1, 20))
        
        # الملخص
        summary_lines = report_data['summary'].strip().split('\n')
        for line in summary_lines:
            if line.strip():
                story.append(Paragraph(line.strip(), normal_style))
        
        story.append(Spacer(1, 30))
        
        # الجدول (إذا كانت البيانات متوفرة)
        if report_data.get('data') and report_data.get('columns'):
            # إنشاء جدول البيانات
            table_data = [report_data['columns']]
            
            for row in report_data['data'][:50]:  # أول 50 صف فقط
                table_data.append([str(cell) for cell in row])
            
            # إنشاء الجدول
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
        
        # بناء المستند
        doc.build(story)
        
        return filename

# إنشاء مثيل من مولد PDF
pdf_generator = PDFGenerator()
