import tkinter as tk
from tkinter import ttk, messagebox, font, filedialog
from datetime import datetime, timedelta
import sqlite3
from database import db

class ReportsWindow:
    def __init__(self, parent):
        self.parent = parent
        self.db = db

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير")
        self.window.geometry("1000x700")
        self.window.configure(bg='#f0f0f0')

        # تكوين الخط العربي
        self.setup_arabic_font()

        # إنشاء الواجهة
        self.create_interface()

    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")

    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#34495e', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="التقارير",
                              font=self.arabic_font_large, fg='white', bg='#34495e')
        title_label.pack(expand=True)

        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار الخيارات (يسار)
        options_frame = tk.LabelFrame(main_frame, text="خيارات التقرير",
                                     font=self.arabic_font_bold, bg='#f0f0f0')
        options_frame.pack(side='left', fill='y', padx=5, pady=5)

        self.create_options_panel(options_frame)

        # إطار النتائج (يمين)
        results_frame = tk.LabelFrame(main_frame, text="نتائج التقرير",
                                     font=self.arabic_font_bold, bg='#f0f0f0')
        results_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)

        self.create_results_panel(results_frame)

    def create_options_panel(self, parent):
        """إنشاء لوحة خيارات التقرير"""
        # نوع التقرير
        tk.Label(parent, text="نوع التقرير:", font=self.arabic_font_bold, bg='#f0f0f0').grid(
            row=0, column=0, sticky='w', padx=5, pady=5)

        self.report_type = tk.StringVar(value="sales")
        report_types = [
            ("تقرير المبيعات", "sales"),
            ("تقرير المشتريات", "purchases"),
            ("تقرير الأرباح والخسائر", "profit_loss"),
            ("تقرير المخزون", "inventory"),
            ("تقرير العملاء", "customers"),
            ("تقرير الموردين", "suppliers")
        ]

        for i, (text, value) in enumerate(report_types):
            tk.Radiobutton(parent, text=text, variable=self.report_type, value=value,
                          font=self.arabic_font, bg='#f0f0f0').grid(
                row=i+1, column=0, sticky='w', padx=20, pady=2)

        # الفترة الزمنية
        tk.Label(parent, text="الفترة الزمنية:", font=self.arabic_font_bold, bg='#f0f0f0').grid(
            row=8, column=0, sticky='w', padx=5, pady=(20, 5))

        # تاريخ البداية
        tk.Label(parent, text="من تاريخ:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=9, column=0, sticky='w', padx=5, pady=2)
        self.start_date_entry = tk.Entry(parent, font=self.arabic_font, width=15)
        self.start_date_entry.grid(row=10, column=0, padx=5, pady=2)
        self.start_date_entry.insert(0, (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))

        # تاريخ النهاية
        tk.Label(parent, text="إلى تاريخ:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=11, column=0, sticky='w', padx=5, pady=2)
        self.end_date_entry = tk.Entry(parent, font=self.arabic_font, width=15)
        self.end_date_entry.grid(row=12, column=0, padx=5, pady=2)
        self.end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # أزرار سريعة للفترات
        periods_frame = tk.Frame(parent, bg='#f0f0f0')
        periods_frame.grid(row=13, column=0, pady=10)

        tk.Button(periods_frame, text="اليوم", font=self.arabic_font,
                 bg='#3498db', fg='white', width=8,
                 command=lambda: self.set_period('today')).pack(pady=2)
        tk.Button(periods_frame, text="هذا الأسبوع", font=self.arabic_font,
                 bg='#3498db', fg='white', width=8,
                 command=lambda: self.set_period('week')).pack(pady=2)
        tk.Button(periods_frame, text="هذا الشهر", font=self.arabic_font,
                 bg='#3498db', fg='white', width=8,
                 command=lambda: self.set_period('month')).pack(pady=2)
        tk.Button(periods_frame, text="هذا العام", font=self.arabic_font,
                 bg='#3498db', fg='white', width=8,
                 command=lambda: self.set_period('year')).pack(pady=2)

        # أزرار العمليات
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.grid(row=14, column=0, pady=20)

        tk.Button(buttons_frame, text="إنشاء التقرير", font=self.arabic_font_bold,
                 bg='#27ae60', fg='white', width=12, height=2,
                 command=self.generate_report).pack(pady=5)

        tk.Button(buttons_frame, text="طباعة", font=self.arabic_font,
                 bg='#e74c3c', fg='white', width=12,
                 command=self.print_report).pack(pady=2)

        tk.Button(buttons_frame, text="حفظ PDF", font=self.arabic_font,
                 bg='#f39c12', fg='white', width=12,
                 command=self.save_pdf).pack(pady=2)

        tk.Button(buttons_frame, text="تصدير Excel", font=self.arabic_font,
                 bg='#9b59b6', fg='white', width=12,
                 command=self.export_excel).pack(pady=2)

    def create_results_panel(self, parent):
        """إنشاء لوحة نتائج التقرير"""
        # إطار الملخص
        summary_frame = tk.LabelFrame(parent, text="ملخص التقرير",
                                     font=self.arabic_font_bold, bg='#f0f0f0')
        summary_frame.pack(fill='x', padx=5, pady=5)

        self.summary_text = tk.Text(summary_frame, font=self.arabic_font, height=4,
                                   bg='#ecf0f1', state='disabled')
        self.summary_text.pack(fill='x', padx=5, pady=5)

        # جدول التفاصيل
        details_frame = tk.LabelFrame(parent, text="تفاصيل التقرير",
                                     font=self.arabic_font_bold, bg='#f0f0f0')
        details_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # إنشاء Treeview للتفاصيل
        self.details_tree = ttk.Treeview(details_frame, show='headings', height=20)

        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_tree.yview)
        scrollbar_h = ttk.Scrollbar(details_frame, orient='horizontal', command=self.details_tree.xview)
        self.details_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # تخطيط الجدول
        self.details_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_v.pack(side='right', fill='y')
        scrollbar_h.pack(side='bottom', fill='x')

        # متغير لحفظ بيانات التقرير
        self.current_report_data = None

    def set_period(self, period):
        """تعيين فترة زمنية محددة"""
        today = datetime.now()

        if period == 'today':
            start_date = today
            end_date = today
        elif period == 'week':
            start_date = today - timedelta(days=today.weekday())
            end_date = today
        elif period == 'month':
            start_date = today.replace(day=1)
            end_date = today
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
            end_date = today

        self.start_date_entry.delete(0, 'end')
        self.start_date_entry.insert(0, start_date.strftime("%Y-%m-%d"))

        self.end_date_entry.delete(0, 'end')
        self.end_date_entry.insert(0, end_date.strftime("%Y-%m-%d"))

    def generate_report(self):
        """إنشاء التقرير"""
        try:
            report_type = self.report_type.get()
            start_date = self.start_date_entry.get()
            end_date = self.end_date_entry.get()

            # التحقق من صحة التواريخ
            try:
                datetime.strptime(start_date, "%Y-%m-%d")
                datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
                return

            # إنشاء التقرير حسب النوع
            if report_type == "sales":
                self.generate_sales_report(start_date, end_date)
            elif report_type == "purchases":
                self.generate_purchases_report(start_date, end_date)
            elif report_type == "profit_loss":
                self.generate_profit_loss_report(start_date, end_date)
            elif report_type == "inventory":
                self.generate_inventory_report()
            elif report_type == "customers":
                self.generate_customers_report()
            elif report_type == "suppliers":
                self.generate_suppliers_report()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def generate_sales_report(self, start_date, end_date):
        """إنشاء تقرير المبيعات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # استعلام المبيعات
        cursor.execute('''
            SELECT s.invoice_number, s.created_at, c.name as customer_name,
                   s.total_amount, s.discount, s.tax_amount, s.final_amount,
                   s.payment_method
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE DATE(s.created_at) BETWEEN ? AND ?
            ORDER BY s.created_at DESC
        ''', (start_date, end_date))

        sales_data = cursor.fetchall()

        # حساب الملخص
        total_sales = sum(sale[6] for sale in sales_data)  # final_amount
        total_discount = sum(sale[4] for sale in sales_data)  # discount
        total_tax = sum(sale[5] for sale in sales_data)  # tax_amount
        sales_count = len(sales_data)

        # عرض الملخص
        summary = f"""
إجمالي المبيعات: {total_sales:.2f} جنيه
عدد الفواتير: {sales_count}
إجمالي الخصومات: {total_discount:.2f} جنيه
إجمالي الضرائب: {total_tax:.2f} جنيه
متوسط قيمة الفاتورة: {(total_sales / sales_count if sales_count > 0 else 0):.2f} جنيه
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ الأساسي', 'الخصم', 'الضريبة', 'الإجمالي النهائي', 'طريقة الدفع')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        for sale in sales_data:
            self.details_tree.insert('', 'end', values=(
                sale[0],  # invoice_number
                sale[1][:10],  # created_at (date only)
                sale[2] or 'عميل نقدي',  # customer_name
                f"{sale[3]:.2f}",  # total_amount
                f"{sale[4]:.2f}",  # discount
                f"{sale[5]:.2f}",  # tax_amount
                f"{sale[6]:.2f}",  # final_amount
                sale[7]  # payment_method
            ))

        self.current_report_data = {
            'type': 'sales',
            'period': f"{start_date} إلى {end_date}",
            'summary': summary,
            'data': sales_data,
            'columns': columns
        }

        conn.close()

    def generate_purchases_report(self, start_date, end_date):
        """إنشاء تقرير المشتريات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # استعلام المشتريات
        cursor.execute('''
            SELECT p.invoice_number, p.created_at, s.name as supplier_name,
                   p.total_amount, p.discount, p.tax_amount, p.final_amount,
                   p.payment_method
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE DATE(p.created_at) BETWEEN ? AND ?
            ORDER BY p.created_at DESC
        ''', (start_date, end_date))

        purchases_data = cursor.fetchall()

        # حساب الملخص
        total_purchases = sum(purchase[6] for purchase in purchases_data)  # final_amount
        total_discount = sum(purchase[4] for purchase in purchases_data)  # discount
        total_tax = sum(purchase[5] for purchase in purchases_data)  # tax_amount
        purchases_count = len(purchases_data)

        # عرض الملخص
        summary = f"""
إجمالي المشتريات: {total_purchases:.2f} جنيه
عدد الفواتير: {purchases_count}
إجمالي الخصومات: {total_discount:.2f} جنيه
إجمالي الضرائب: {total_tax:.2f} جنيه
متوسط قيمة الفاتورة: {(total_purchases / purchases_count if purchases_count > 0 else 0):.2f} جنيه
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('رقم الفاتورة', 'التاريخ', 'المورد', 'المبلغ الأساسي', 'الخصم', 'الضريبة', 'الإجمالي النهائي', 'طريقة الدفع')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        for purchase in purchases_data:
            self.details_tree.insert('', 'end', values=(
                purchase[0],  # invoice_number
                purchase[1][:10],  # created_at (date only)
                purchase[2] or 'غير محدد',  # supplier_name
                f"{purchase[3]:.2f}",  # total_amount
                f"{purchase[4]:.2f}",  # discount
                f"{purchase[5]:.2f}",  # tax_amount
                f"{purchase[6]:.2f}",  # final_amount
                purchase[7]  # payment_method
            ))

        self.current_report_data = {
            'type': 'purchases',
            'period': f"{start_date} إلى {end_date}",
            'summary': summary,
            'data': purchases_data,
            'columns': columns
        }

        conn.close()

    def generate_profit_loss_report(self, start_date, end_date):
        """إنشاء تقرير الأرباح والخسائر"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # حساب إجمالي المبيعات
        cursor.execute('''
            SELECT COALESCE(SUM(final_amount), 0) as total_sales
            FROM sales
            WHERE DATE(created_at) BETWEEN ? AND ?
        ''', (start_date, end_date))
        total_sales = cursor.fetchone()[0]

        # حساب تكلفة البضاعة المباعة
        cursor.execute('''
            SELECT COALESCE(SUM(si.quantity * p.purchase_price), 0) as cost_of_goods
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN products p ON si.product_id = p.id
            WHERE DATE(s.created_at) BETWEEN ? AND ?
        ''', (start_date, end_date))
        cost_of_goods = cursor.fetchone()[0]

        # حساب إجمالي المشتريات
        cursor.execute('''
            SELECT COALESCE(SUM(final_amount), 0) as total_purchases
            FROM purchases
            WHERE DATE(created_at) BETWEEN ? AND ?
        ''', (start_date, end_date))
        total_purchases = cursor.fetchone()[0]

        # حساب المصروفات
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0) as total_expenses
            FROM expenses
            WHERE DATE(created_at) BETWEEN ? AND ?
        ''', (start_date, end_date))
        total_expenses = cursor.fetchone()[0]

        # حساب الإيرادات الأخرى
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0) as other_income
            FROM other_income
            WHERE DATE(created_at) BETWEEN ? AND ?
        ''', (start_date, end_date))
        other_income = cursor.fetchone()[0]

        # حساب الأرباح
        gross_profit = total_sales - cost_of_goods
        net_profit = gross_profit - total_expenses + other_income
        profit_margin = (gross_profit / total_sales * 100) if total_sales > 0 else 0

        # عرض الملخص
        summary = f"""
إجمالي المبيعات: {total_sales:.2f} جنيه
تكلفة البضاعة المباعة: {cost_of_goods:.2f} جنيه
الربح الإجمالي: {gross_profit:.2f} جنيه
المصروفات: {total_expenses:.2f} جنيه
الإيرادات الأخرى: {other_income:.2f} جنيه
صافي الربح: {net_profit:.2f} جنيه
هامش الربح: {profit_margin:.1f}%
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('البيان', 'المبلغ', 'النسبة من المبيعات')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        profit_data = [
            ('إجمالي المبيعات', total_sales, 100.0),
            ('تكلفة البضاعة المباعة', cost_of_goods, (cost_of_goods/total_sales*100) if total_sales > 0 else 0),
            ('الربح الإجمالي', gross_profit, (gross_profit/total_sales*100) if total_sales > 0 else 0),
            ('المصروفات', total_expenses, (total_expenses/total_sales*100) if total_sales > 0 else 0),
            ('الإيرادات الأخرى', other_income, (other_income/total_sales*100) if total_sales > 0 else 0),
            ('صافي الربح', net_profit, (net_profit/total_sales*100) if total_sales > 0 else 0)
        ]

        for item, amount, percentage in profit_data:
            self.details_tree.insert('', 'end', values=(
                item,
                f"{amount:.2f}",
                f"{percentage:.1f}%"
            ))

        self.current_report_data = {
            'type': 'profit_loss',
            'period': f"{start_date} إلى {end_date}",
            'summary': summary,
            'data': profit_data,
            'columns': columns
        }

        conn.close()

    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # استعلام المخزون
        cursor.execute('''
            SELECT p.name, c.name as category_name, p.quantity, p.min_quantity,
                   p.purchase_price, p.selling_price, p.unit,
                   (p.quantity * p.purchase_price) as total_cost,
                   (p.quantity * p.selling_price) as total_value
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.name
        ''')

        inventory_data = cursor.fetchall()

        # حساب الملخص
        total_items = len(inventory_data)
        total_quantity = sum(item[2] for item in inventory_data)
        total_cost = sum(item[7] for item in inventory_data)
        total_value = sum(item[8] for item in inventory_data)
        low_stock_items = len([item for item in inventory_data if item[2] <= item[3]])

        # عرض الملخص
        summary = f"""
إجمالي الأصناف: {total_items}
إجمالي الكمية: {total_quantity}
إجمالي تكلفة المخزون: {total_cost:.2f} جنيه
إجمالي قيمة المخزون: {total_value:.2f} جنيه
الأصناف منخفضة المخزون: {low_stock_items}
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('اسم المنتج', 'التصنيف', 'الكمية', 'الحد الأدنى', 'سعر الشراء', 'سعر البيع', 'الوحدة', 'إجمالي التكلفة', 'إجمالي القيمة')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        for item in inventory_data:
            # تحديد لون الصف حسب المخزون
            tags = ()
            if item[2] <= item[3]:  # quantity <= min_quantity
                tags = ('low_stock',)

            self.details_tree.insert('', 'end', values=(
                item[0],  # name
                item[1] or 'غير محدد',  # category_name
                item[2],  # quantity
                item[3],  # min_quantity
                f"{item[4]:.2f}",  # purchase_price
                f"{item[5]:.2f}",  # selling_price
                item[6],  # unit
                f"{item[7]:.2f}",  # total_cost
                f"{item[8]:.2f}"   # total_value
            ), tags=tags)

        # تكوين ألوان الصفوف
        self.details_tree.tag_configure('low_stock', background='#ffcccc')

        self.current_report_data = {
            'type': 'inventory',
            'period': 'حالي',
            'summary': summary,
            'data': inventory_data,
            'columns': columns
        }

        conn.close()

    def generate_customers_report(self):
        """إنشاء تقرير العملاء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # استعلام العملاء مع إحصائياتهم
        cursor.execute('''
            SELECT c.name, c.phone, c.email, c.balance,
                   COUNT(s.id) as total_orders,
                   COALESCE(SUM(s.final_amount), 0) as total_purchases,
                   COALESCE(MAX(s.created_at), 'لا يوجد') as last_order
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id
            GROUP BY c.id, c.name, c.phone, c.email, c.balance
            ORDER BY total_purchases DESC
        ''')

        customers_data = cursor.fetchall()

        # حساب الملخص
        total_customers = len(customers_data)
        active_customers = len([c for c in customers_data if c[4] > 0])  # customers with orders
        total_customer_value = sum(c[5] for c in customers_data)  # total purchases
        total_balance = sum(c[3] for c in customers_data)  # total balance

        # عرض الملخص
        summary = f"""
إجمالي العملاء: {total_customers}
العملاء النشطين: {active_customers}
إجمالي مشتريات العملاء: {total_customer_value:.2f} جنيه
إجمالي أرصدة العملاء: {total_balance:.2f} جنيه
متوسط قيمة العميل: {(total_customer_value / total_customers if total_customers > 0 else 0):.2f} جنيه
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('اسم العميل', 'الهاتف', 'البريد الإلكتروني', 'الرصيد', 'عدد الطلبات', 'إجمالي المشتريات', 'آخر طلب')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        for customer in customers_data:
            self.details_tree.insert('', 'end', values=(
                customer[0],  # name
                customer[1] or '',  # phone
                customer[2] or '',  # email
                f"{customer[3]:.2f}",  # balance
                customer[4],  # total_orders
                f"{customer[5]:.2f}",  # total_purchases
                customer[6][:10] if customer[6] != 'لا يوجد' else customer[6]  # last_order
            ))

        self.current_report_data = {
            'type': 'customers',
            'period': 'حالي',
            'summary': summary,
            'data': customers_data,
            'columns': columns
        }

        conn.close()

    def generate_suppliers_report(self):
        """إنشاء تقرير الموردين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # استعلام الموردين مع إحصائياتهم
        cursor.execute('''
            SELECT s.name, s.phone, s.email, s.balance,
                   COUNT(p.id) as total_orders,
                   COALESCE(SUM(p.final_amount), 0) as total_purchases,
                   COALESCE(MAX(p.created_at), 'لا يوجد') as last_order
            FROM suppliers s
            LEFT JOIN purchases p ON s.id = p.supplier_id
            GROUP BY s.id, s.name, s.phone, s.email, s.balance
            ORDER BY total_purchases DESC
        ''')

        suppliers_data = cursor.fetchall()

        # حساب الملخص
        total_suppliers = len(suppliers_data)
        active_suppliers = len([s for s in suppliers_data if s[4] > 0])  # suppliers with orders
        total_supplier_value = sum(s[5] for s in suppliers_data)  # total purchases
        total_balance = sum(s[3] for s in suppliers_data)  # total balance

        # عرض الملخص
        summary = f"""
إجمالي الموردين: {total_suppliers}
الموردين النشطين: {active_suppliers}
إجمالي مشتريات من الموردين: {total_supplier_value:.2f} جنيه
إجمالي أرصدة الموردين: {total_balance:.2f} جنيه
متوسط قيمة المورد: {(total_supplier_value / total_suppliers if total_suppliers > 0 else 0):.2f} جنيه
        """

        self.update_summary(summary)

        # إعداد أعمدة الجدول
        columns = ('اسم المورد', 'الهاتف', 'البريد الإلكتروني', 'الرصيد', 'عدد الطلبات', 'إجمالي المشتريات', 'آخر طلب')
        self.setup_tree_columns(columns)

        # إضافة البيانات
        for supplier in suppliers_data:
            self.details_tree.insert('', 'end', values=(
                supplier[0],  # name
                supplier[1] or '',  # phone
                supplier[2] or '',  # email
                f"{supplier[3]:.2f}",  # balance
                supplier[4],  # total_orders
                f"{supplier[5]:.2f}",  # total_purchases
                supplier[6][:10] if supplier[6] != 'لا يوجد' else supplier[6]  # last_order
            ))

        self.current_report_data = {
            'type': 'suppliers',
            'period': 'حالي',
            'summary': summary,
            'data': suppliers_data,
            'columns': columns
        }

        conn.close()

    def update_summary(self, summary_text):
        """تحديث نص الملخص"""
        self.summary_text.config(state='normal')
        self.summary_text.delete('1.0', 'end')
        self.summary_text.insert('1.0', summary_text)
        self.summary_text.config(state='disabled')

    def setup_tree_columns(self, columns):
        """إعداد أعمدة الجدول"""
        # مسح الأعمدة الحالية
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)

        # إعداد الأعمدة الجديدة
        self.details_tree['columns'] = columns

        for col in columns:
            self.details_tree.heading(col, text=col)
            self.details_tree.column(col, width=120, anchor='center')

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report_data:
            messagebox.showerror("خطأ", "لا يوجد تقرير لطباعته")
            return

        messagebox.showinfo("قريباً", "ميزة الطباعة قيد التطوير")

    def save_pdf(self):
        """حفظ التقرير كـ PDF"""
        if not self.current_report_data:
            messagebox.showerror("خطأ", "لا يوجد تقرير لحفظه")
            return

        try:
            from pdf_generator import pdf_generator

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير"
            )

            if filename:
                # إنشاء ملف PDF
                saved_file = pdf_generator.generate_report_pdf(self.current_report_data, filename)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{saved_file}")

                # فتح الملف
                import os
                os.startfile(saved_file)

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة ReportLab غير مثبتة\nقم بتثبيتها باستخدام: pip install reportlab")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def export_excel(self):
        """تصدير التقرير إلى Excel"""
        if not self.current_report_data:
            messagebox.showerror("خطأ", "لا يوجد تقرير لتصديره")
            return

        try:
            import pandas as pd

            # تحضير البيانات
            data = self.current_report_data['data']
            columns = self.current_report_data['columns']

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=columns)

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ التقرير"
            )

            if file_path:
                # حفظ الملف
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='التقرير', index=False)

                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{file_path}")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة pandas غير مثبتة\nقم بتثبيتها باستخدام: pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير التقرير: {str(e)}")