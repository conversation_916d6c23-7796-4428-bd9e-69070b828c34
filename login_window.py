import tkinter as tk
from tkinter import ttk, messagebox, font
from auth_system import auth_system

class LoginWindow:
    def __init__(self, on_success_callback=None):
        self.on_success_callback = on_success_callback
        self.login_successful = False
        
        # إنشاء النافذة
        self.window = tk.Tk()
        self.window.title("تسجيل الدخول - نظام إدارة المحل")
        self.window.geometry("400x300")
        self.window.configure(bg='#2c3e50')
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # ربط Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda e: self.login())
        
        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=16, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=16, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#2c3e50')
        main_frame.pack(expand=True, fill='both', padx=30, pady=30)

        # شعار أو عنوان
        title_label = tk.Label(main_frame, text="🏪 نظام إدارة المحل",
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='#ecf0f1', relief='raised', bd=3)
        form_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # عنوان تسجيل الدخول
        login_title = tk.Label(form_frame, text="تسجيل الدخول",
                              font=self.arabic_font_bold, fg='#2c3e50', bg='#ecf0f1')
        login_title.pack(pady=(15, 20))

        # إطار الحقول
        fields_frame = tk.Frame(form_frame, bg='#ecf0f1')
        fields_frame.pack(padx=30, pady=10)

        # اسم المستخدم
        username_frame = tk.Frame(fields_frame, bg='#ecf0f1')
        username_frame.pack(fill='x', pady=(0, 15))

        tk.Label(username_frame, text="👤 اسم المستخدم:", font=self.arabic_font_bold,
                fg='#2c3e50', bg='#ecf0f1').pack(anchor='w', pady=(0, 5))

        self.username_entry = tk.Entry(username_frame, font=self.arabic_font, width=30,
                                      relief='solid', bd=2, bg='white')
        self.username_entry.pack(fill='x', ipady=5)

        # كلمة المرور
        password_frame = tk.Frame(fields_frame, bg='#ecf0f1')
        password_frame.pack(fill='x', pady=(0, 20))

        tk.Label(password_frame, text="🔒 كلمة المرور:", font=self.arabic_font_bold,
                fg='#2c3e50', bg='#ecf0f1').pack(anchor='w', pady=(0, 5))

        self.password_entry = tk.Entry(password_frame, font=self.arabic_font, width=30,
                                      show='●', relief='solid', bd=2, bg='white')
        self.password_entry.pack(fill='x', ipady=5)

        # أزرار
        buttons_frame = tk.Frame(form_frame, bg='#ecf0f1')
        buttons_frame.pack(pady=(10, 20))

        # زر تسجيل الدخول
        login_btn = tk.Button(buttons_frame, text="🔑 دخول", font=self.arabic_font_bold,
                             bg='#27ae60', fg='white', width=15, height=2,
                             command=self.login, relief='flat', cursor='hand2')
        login_btn.pack(side='left', padx=10)

        # تأثير التمرير للزر
        def on_enter_login(e):
            login_btn.config(bg='#229954')
        def on_leave_login(e):
            login_btn.config(bg='#27ae60')
        login_btn.bind('<Enter>', on_enter_login)
        login_btn.bind('<Leave>', on_leave_login)

        # زر إلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", font=self.arabic_font,
                              bg='#e74c3c', fg='white', width=15, height=2,
                              command=self.cancel, relief='flat', cursor='hand2')
        cancel_btn.pack(side='left', padx=10)

        # تأثير التمرير للزر
        def on_enter_cancel(e):
            cancel_btn.config(bg='#cb4335')
        def on_leave_cancel(e):
            cancel_btn.config(bg='#e74c3c')
        cancel_btn.bind('<Enter>', on_enter_cancel)
        cancel_btn.bind('<Leave>', on_leave_cancel)

        # رسالة المساعدة
        help_frame = tk.Frame(main_frame, bg='#2c3e50')
        help_frame.pack(fill='x', pady=(10, 0))

        help_text = "💡 المدير الافتراضي: admin / admin123"
        help_label = tk.Label(help_frame, text=help_text, font=self.arabic_font,
                             fg='#f39c12', bg='#2c3e50')
        help_label.pack()

        # معلومات إضافية
        info_text = "📖 للحصول على المساعدة، اضغط F12 بعد تسجيل الدخول"
        info_label = tk.Label(help_frame, text=info_text, font=self.arabic_font,
                             fg='#95a5a6', bg='#2c3e50')
        info_label.pack(pady=(5, 0))
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        # تغيير لون الحقول للحالة الطبيعية
        self.username_entry.config(bg='white', relief='solid')
        self.password_entry.config(bg='white', relief='solid')

        if not username:
            self.username_entry.config(bg='#ffebee', relief='solid')
            messagebox.showerror("خطأ", "⚠️ يجب إدخال اسم المستخدم")
            self.username_entry.focus()
            return

        if not password:
            self.password_entry.config(bg='#ffebee', relief='solid')
            messagebox.showerror("خطأ", "⚠️ يجب إدخال كلمة المرور")
            self.password_entry.focus()
            return

        # تعطيل الأزرار أثناء المعالجة
        login_btn = None
        cancel_btn = None
        for widget in self.window.winfo_children():
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    if hasattr(child, 'winfo_children'):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, tk.Frame):
                                for button in grandchild.winfo_children():
                                    if isinstance(button, tk.Button):
                                        if "دخول" in button.cget('text'):
                                            login_btn = button
                                            button.config(text="⏳ جاري التحقق...", state='disabled')
                                        elif "إلغاء" in button.cget('text'):
                                            cancel_btn = button
                                            button.config(state='disabled')

        # تحديث النافذة
        self.window.update()

        # محاولة تسجيل الدخول
        success, message = auth_system.login(username, password)

        # إعادة تفعيل الأزرار
        if login_btn:
            login_btn.config(text="🔑 دخول", state='normal')
        if cancel_btn:
            cancel_btn.config(state='normal')

        if success:
            self.login_successful = True
            # إظهار رسالة نجاح مع أيقونة
            messagebox.showinfo("نجح", f"✅ {message}")

            # استدعاء دالة النجاح إذا كانت موجودة
            if self.on_success_callback:
                self.on_success_callback()

            self.window.destroy()
        else:
            # تمييز الحقول بالأحمر عند الخطأ
            self.username_entry.config(bg='#ffebee', relief='solid')
            self.password_entry.config(bg='#ffebee', relief='solid')
            messagebox.showerror("خطأ", f"❌ {message}")
            self.password_entry.delete(0, 'end')
            self.username_entry.focus()
    
    def cancel(self):
        """إلغاء تسجيل الدخول"""
        self.window.destroy()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window.mainloop()
        return self.login_successful

class UserManagementWindow:
    def __init__(self, parent):
        self.parent = parent
        
        # التحقق من الصلاحية
        if not auth_system.has_permission('view_users'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض هذه النافذة")
            return
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المستخدمين")
        self.window.geometry("900x600")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_users()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المستخدمين"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#3498db', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة المستخدمين", 
                              font=self.arabic_font_bold, fg='white', bg='#3498db')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار النموذج (أعلى)
        if auth_system.has_permission('manage_users'):
            form_frame = tk.LabelFrame(main_frame, text="إضافة مستخدم جديد", 
                                      font=self.arabic_font_bold, bg='#f0f0f0')
            form_frame.pack(fill='x', padx=5, pady=5)
            
            self.create_user_form(form_frame)
        
        # إطار قائمة المستخدمين
        list_frame = tk.LabelFrame(main_frame, text="قائمة المستخدمين", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.create_users_list(list_frame)
    
    def create_user_form(self, parent):
        """إنشاء نموذج إضافة المستخدم"""
        inner_frame = tk.Frame(parent, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول
        row1_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=5)
        
        # اسم المستخدم
        tk.Label(row1_frame, text="اسم المستخدم:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.username_entry = tk.Entry(row1_frame, font=self.arabic_font, width=20)
        self.username_entry.pack(side='left', padx=10)
        
        # الاسم الكامل
        tk.Label(row1_frame, text="الاسم الكامل:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.fullname_entry = tk.Entry(row1_frame, font=self.arabic_font, width=25)
        self.fullname_entry.pack(side='left', padx=10)
        
        # الصف الثاني
        row2_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=5)
        
        # كلمة المرور
        tk.Label(row2_frame, text="كلمة المرور:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.password_entry = tk.Entry(row2_frame, font=self.arabic_font, width=20, show='*')
        self.password_entry.pack(side='left', padx=10)
        
        # الدور
        tk.Label(row2_frame, text="الدور:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.role_combo = ttk.Combobox(row2_frame, font=self.arabic_font, width=15, state='readonly')
        self.role_combo['values'] = [
            ('admin', 'مدير'),
            ('manager', 'مدير مساعد'),
            ('cashier', 'كاشير'),
            ('inventory', 'مخزن')
        ]
        self.role_combo.pack(side='left', padx=10)
        
        # أزرار
        buttons_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=10)
        
        tk.Button(buttons_frame, text="إضافة مستخدم", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=self.add_user).pack(side='left', padx=5)
        
        tk.Button(buttons_frame, text="مسح النموذج", font=self.arabic_font,
                 bg='#95a5a6', fg='white', command=self.clear_form).pack(side='left', padx=5)
    
    def create_users_list(self, parent):
        """إنشاء قائمة المستخدمين"""
        # جدول المستخدمين
        columns = ('ID', 'اسم المستخدم', 'الاسم الكامل', 'الدور', 'الحالة', 'آخر دخول')
        self.users_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
        
        self.users_tree.column('ID', width=50)
        self.users_tree.column('اسم المستخدم', width=120)
        self.users_tree.column('الاسم الكامل', width=150)
        self.users_tree.column('الدور', width=100)
        self.users_tree.column('الحالة', width=80)
        self.users_tree.column('آخر دخول', width=150)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # قائمة السياق
        self.create_context_menu()
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        
        if auth_system.has_permission('manage_users'):
            self.context_menu.add_command(label="تعديل", command=self.edit_user)
            self.context_menu.add_command(label="تغيير كلمة المرور", command=self.change_password)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="تفعيل/تعطيل", command=self.toggle_user_status)
        
        if auth_system.has_permission('delete_user'):
            self.context_menu.add_command(label="حذف", command=self.delete_user)
        
        self.context_menu.add_separator()
        self.context_menu.add_command(label="عرض الجلسات", command=self.view_sessions)
        
        # ربط القائمة بالجدول
        self.users_tree.bind('<Button-3>', self.show_context_menu)
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        selection = self.users_tree.selection()
        if selection:
            self.context_menu.tk_popup(event.x_root, event.y_root)
    
    def load_users(self):
        """تحميل المستخدمين"""
        # مسح الجدول
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # تحميل المستخدمين
        users = auth_system.get_all_users()
        
        role_names = {
            'admin': 'مدير',
            'manager': 'مدير مساعد',
            'cashier': 'كاشير',
            'inventory': 'مخزن'
        }
        
        for user in users:
            status = 'نشط' if user[4] else 'معطل'
            last_login = user[6][:16] if user[6] else 'لم يسجل دخول'
            
            self.users_tree.insert('', 'end', values=(
                user[0],  # ID
                user[1],  # username
                user[2],  # full_name
                role_names.get(user[3], user[3]),  # role
                status,   # is_active
                last_login  # last_login
            ))
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        username = self.username_entry.get().strip()
        fullname = self.fullname_entry.get().strip()
        password = self.password_entry.get()
        role_selection = self.role_combo.get()
        
        if not username or not fullname or not password or not role_selection:
            messagebox.showerror("خطأ", "يجب ملء جميع الحقول")
            return
        
        # استخراج قيمة الدور
        role = role_selection.split(' - ')[0] if ' - ' in role_selection else role_selection
        
        success, message = auth_system.add_user(username, password, fullname, role)
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_users()
        else:
            messagebox.showerror("خطأ", message)
    
    def clear_form(self):
        """مسح النموذج"""
        self.username_entry.delete(0, 'end')
        self.fullname_entry.delete(0, 'end')
        self.password_entry.delete(0, 'end')
        self.role_combo.set('')
    
    def edit_user(self):
        """تعديل مستخدم"""
        messagebox.showinfo("قريباً", "ميزة تعديل المستخدم قيد التطوير")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        messagebox.showinfo("قريباً", "ميزة تغيير كلمة المرور قيد التطوير")
    
    def toggle_user_status(self):
        """تفعيل/تعطيل المستخدم"""
        messagebox.showinfo("قريباً", "ميزة تفعيل/تعطيل المستخدم قيد التطوير")
    
    def delete_user(self):
        """حذف مستخدم"""
        messagebox.showinfo("قريباً", "ميزة حذف المستخدم قيد التطوير")
    
    def view_sessions(self):
        """عرض جلسات المستخدم"""
        messagebox.showinfo("قريباً", "ميزة عرض الجلسات قيد التطوير")
