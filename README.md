# نظام إدارة المحل

نظام شامل لإدارة المحلات التجارية باللغة العربية، مطور باستخدام Python و Tkinter.

## المميزات الرئيسية

### 1. إدارة الأصناف (المخزون)
- ✅ إضافة / تعديل / حذف المنتجات
- ✅ تصنيف المنتجات (مواد غذائية، أدوات، مشروبات، إلخ)
- ✅ متابعة الكمية في المخزون
- ✅ تنبيه عند انخفاض الكمية
- ✅ البحث في المنتجات بالاسم أو الباركود

### 2. المبيعات
- ✅ نقطة البيع (POS) سهلة الاستخدام
- ✅ البحث عن المنتجات أثناء البيع
- ✅ حساب الإجمالي، الخصم، والضريبة
- ✅ ربط الفاتورة بالعميل (اختياري)
- ✅ تحديث المخزون تلقائياً بعد البيع

### 3. المشتريات
- ✅ تسجيل فاتورة شراء من مورد
- ✅ تحديث كمية المخزون تلقائياً
- ✅ ربط الفاتورة بالمورد
- ✅ حساب الخصم والضريبة

### 4. العملاء والموردين
- ✅ إضافة بيانات العملاء (اسم، هاتف، عنوان، ملاحظات)
- ✅ إضافة بيانات الموردين
- ✅ متابعة أرصدة العملاء والموردين
- 🔄 عرض كشف حساب لكل عميل أو مورد (قيد التطوير)

### 5. المصروفات والإيرادات
- 🔄 تسجيل أنواع المصروفات (رواتب، كهرباء، إيجار) (قيد التطوير)
- 🔄 تسجيل الإيرادات الأخرى (قيد التطوير)
- 🔄 تقارير شاملة بالأرباح والخسائر (قيد التطوير)

### 6. التقارير
- 🔄 تقرير المبيعات اليومي/الشهري/السنوي (قيد التطوير)
- 🔄 تقرير الأرباح والخسائر (قيد التطوير)
- 🔄 تقرير المشتريات (قيد التطوير)
- 🔄 تقرير الديون والمدفوعات (قيد التطوير)
- ✅ تقرير الكميات في المخزون (متوفر في الواجهة الرئيسية)

### 7. النسخ الاحتياطي
- ✅ إنشاء نسخة احتياطية من قاعدة البيانات
- ✅ استعادة قاعدة البيانات من نسخة احتياطية

## المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.7 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

### المكتبات الإضافية (اختيارية)
```bash
pip install reportlab fpdf2 matplotlib pandas Pillow python-bidi arabic-reshaper
```

## التشغيل

1. تأكد من تثبيت Python على النظام
2. قم بتشغيل الملف الرئيسي:
```bash
python main.py
```

## هيكل المشروع

```
shop/
├── main.py                 # الملف الرئيسي
├── database.py            # إعداد قاعدة البيانات
├── models.py              # نماذج البيانات
├── products_window.py     # نافذة إدارة المنتجات
├── pos_window.py          # نافذة نقطة البيع
├── customers_window.py    # نافذة إدارة العملاء
├── suppliers_window.py    # نافذة إدارة الموردين
├── purchase_window.py     # نافذة المشتريات
├── categories_window.py   # نافذة إدارة التصنيفات
├── requirements.txt       # المكتبات المطلوبة
├── shop_management.db     # قاعدة البيانات (تُنشأ تلقائياً)
└── README.md             # هذا الملف
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **categories**: تصنيفات المنتجات
- **products**: المنتجات والمخزون
- **customers**: بيانات العملاء
- **suppliers**: بيانات الموردين
- **sales**: فواتير المبيعات
- **sale_items**: تفاصيل فواتير المبيعات
- **purchases**: فواتير المشتريات
- **purchase_items**: تفاصيل فواتير المشتريات
- **expenses**: المصروفات
- **other_income**: الإيرادات الأخرى
- **users**: المستخدمين والصلاحيات

## الاستخدام

### البدء السريع

1. **إضافة تصنيفات**: ابدأ بإضافة تصنيفات للمنتجات من قائمة "المنتجات" > "إدارة التصنيفات"
2. **إضافة منتجات**: أضف المنتجات مع أسعارها وكمياتها من "إدارة المنتجات"
3. **إضافة عملاء وموردين**: أضف بيانات العملاء والموردين من القوائم المخصصة
4. **البدء في البيع**: استخدم "نقطة البيع" لإنشاء فواتير المبيعات
5. **تسجيل المشتريات**: استخدم "فاتورة شراء" لتسجيل المشتريات وتحديث المخزون

### نصائح الاستخدام

- **البحث السريع**: يمكن البحث في المنتجات بالاسم أو الباركود في جميع النوافذ
- **النقر المزدوج**: انقر نقراً مزدوجاً على أي منتج لإضافته للفاتورة
- **تحديث المخزون**: يتم تحديث المخزون تلقائياً عند البيع أو الشراء
- **التنبيهات**: ستظهر تنبيهات في الشاشة الرئيسية عند انخفاض مخزون أي منتج

## الميزات المستقبلية

- 🔄 طباعة الفواتير وحفظها كـ PDF
- 🔄 تقارير مفصلة للمبيعات والأرباح
- 🔄 نظام صلاحيات المستخدمين
- 🔄 دعم الباركود
- 🔄 ربط بآلة طباعة الفواتير
- 🔄 إشعارات الديون والتنبيهات
- 🔄 تصدير البيانات إلى Excel

## الدعم والمساعدة

هذا النظام مطور للاستخدام المحلي في المحلات التجارية الصغيرة والمتوسطة. 

### المشاكل الشائعة

1. **خطأ في فتح قاعدة البيانات**: تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
2. **مشاكل في الخط العربي**: قد تحتاج لتثبيت خطوط عربية إضافية على النظام
3. **بطء في الأداء**: قم بعمل نسخة احتياطية وحذف البيانات القديمة غير المطلوبة

## الترخيص

هذا البرنامج مطور للاستخدام التعليمي والتجاري الحر.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025  
**الإصدار**: 1.0
