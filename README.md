# نظام إدارة المحل - الإصدار 3.0.0 🏪

نظام شامل ومتطور لإدارة المحلات التجارية الصغيرة والمتوسطة باللغة العربية، مطور بـ Python و Tkinter مع قاعدة بيانات SQLite. يتضمن نظام مصادقة متقدم، إشعارات ذكية، مخزون متطور، وواجهة مستخدم محسنة.

## 🌟 الميزات الجديدة في الإصدار 3.0.0

### 🔐 نظام المصادقة والصلاحيات
- **تسجيل دخول محسن** مع واجهة جذابة وآمنة
- **4 مستويات صلاحيات**: مدير، مدير مساعد، كاشير، مخزن
- **إدارة المستخدمين** الكاملة مع تتبع الجلسات
- **حماية النوافذ** حسب صلاحيات المستخدم

### 🔔 نظام الإشعارات الذكي
- **تنبيهات المخزون** المنخفض مع الأولويات
- **إشعارات الديون** للعملاء والموردين
- **ملخص المبيعات** اليومية والأداء
- **واجهة تفاعلية** مع تصفية وإجراءات سريعة

### 📦 المخزون المتقدم
- **إدارة الدفعات** مع تواريخ الانتهاء
- **تتبع حركات المخزون** التفصيلية
- **نظام الجرد** الدوري المتقدم
- **تحويلات المخزون** بين المواقع

### 🎨 واجهة المستخدم المحسنة
- **أيقونات نصية** شاملة (50+ أيقونة)
- **أزرار محسنة** مع تأثيرات التمرير
- **بطاقات وشارات** حالة ملونة
- **اختصارات لوحة المفاتيح** (F1-F12)

---

## 📋 المميزات الأساسية

### 1. إدارة الأصناف (المخزون)
- ✅ إضافة / تعديل / حذف المنتجات
- ✅ تصنيف المنتجات (مواد غذائية، أدوات، مشروبات، إلخ)
- ✅ متابعة الكمية في المخزون
- ✅ تنبيه عند انخفاض الكمية
- ✅ البحث في المنتجات بالاسم أو الباركود

### 2. المبيعات
- ✅ نقطة البيع (POS) سهلة الاستخدام
- ✅ البحث عن المنتجات أثناء البيع
- ✅ حساب الإجمالي، الخصم، والضريبة
- ✅ ربط الفاتورة بالعميل (اختياري)
- ✅ تحديث المخزون تلقائياً بعد البيع

### 3. المشتريات
- ✅ تسجيل فاتورة شراء من مورد
- ✅ تحديث كمية المخزون تلقائياً
- ✅ ربط الفاتورة بالمورد
- ✅ حساب الخصم والضريبة

### 4. العملاء والموردين
- ✅ إضافة بيانات العملاء (اسم، هاتف، عنوان، ملاحظات)
- ✅ إضافة بيانات الموردين
- ✅ متابعة أرصدة العملاء والموردين
- ✅ عرض كشف حساب مفصل لكل عميل أو مورد
- ✅ طباعة كشوف الحسابات وحفظها PDF

### 5. المصروفات والإيرادات
- ✅ تسجيل أنواع المصروفات (رواتب، كهرباء، إيجار، صيانة، إلخ)
- ✅ تسجيل الإيرادات الأخرى (خدمات إضافية، عمولات، فوائد)
- ✅ تصنيف المصروفات والإيرادات
- ✅ ملخص شامل للمصروفات والإيرادات بالفترات الزمنية
- ✅ تقارير مفصلة بالأرباح والخسائر

### 6. التقارير المتقدمة
- ✅ تقرير المبيعات اليومي/الشهري/السنوي مع التفاصيل
- ✅ تقرير الأرباح والخسائر الشامل
- ✅ تقرير المشتريات مع تحليل الموردين
- ✅ تقرير العملاء مع إحصائيات الشراء
- ✅ تقرير الموردين مع تحليل المشتريات
- ✅ تقرير المخزون مع التنبيهات
- ✅ طباعة جميع التقارير وحفظها PDF
- ✅ تصدير التقارير إلى Excel

### 7. الطباعة وحفظ PDF
- ✅ طباعة فواتير المبيعات وحفظها PDF
- ✅ طباعة فواتير المشتريات وحفظها PDF
- ✅ طباعة جميع التقارير وحفظها PDF
- ✅ طباعة كشوف الحسابات
- ✅ تصميم احترافي للفواتير والتقارير باللغة العربية

### 8. النسخ الاحتياطي
- ✅ إنشاء نسخة احتياطية من قاعدة البيانات
- ✅ استعادة قاعدة البيانات من نسخة احتياطية

## المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.7 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

### المكتبات الإضافية (مطلوبة للميزات المتقدمة)
```bash
pip install reportlab matplotlib pandas Pillow python-bidi arabic-reshaper openpyxl
```

## التشغيل

1. تأكد من تثبيت Python على النظام
2. قم بتشغيل الملف الرئيسي:
```bash
python main.py
```

## هيكل المشروع

```
shop/
├── main.py                 # الملف الرئيسي
├── database.py            # إعداد قاعدة البيانات
├── models.py              # نماذج البيانات
├── products_window.py     # نافذة إدارة المنتجات
├── pos_window.py          # نافذة نقطة البيع
├── customers_window.py    # نافذة إدارة العملاء
├── suppliers_window.py    # نافذة إدارة الموردين
├── purchase_window.py     # نافذة المشتريات
├── categories_window.py   # نافذة إدارة التصنيفات
├── reports_window.py      # نافذة التقارير المتقدمة
├── expenses_window.py     # نافذة المصروفات والإيرادات
├── accounts_window.py     # نافذة كشوف الحسابات
├── pdf_generator.py       # مولد ملفات PDF
├── requirements.txt       # المكتبات المطلوبة
├── shop_management.db     # قاعدة البيانات (تُنشأ تلقائياً)
└── README.md             # هذا الملف
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **categories**: تصنيفات المنتجات
- **products**: المنتجات والمخزون
- **customers**: بيانات العملاء
- **suppliers**: بيانات الموردين
- **sales**: فواتير المبيعات
- **sale_items**: تفاصيل فواتير المبيعات
- **purchases**: فواتير المشتريات
- **purchase_items**: تفاصيل فواتير المشتريات
- **expenses**: المصروفات
- **other_income**: الإيرادات الأخرى
- **users**: المستخدمين والصلاحيات

## الاستخدام

### البدء السريع

1. **إضافة تصنيفات**: ابدأ بإضافة تصنيفات للمنتجات من قائمة "المنتجات" > "إدارة التصنيفات"
2. **إضافة منتجات**: أضف المنتجات مع أسعارها وكمياتها من "إدارة المنتجات"
3. **إضافة عملاء وموردين**: أضف بيانات العملاء والموردين من القوائم المخصصة
4. **البدء في البيع**: استخدم "نقطة البيع" لإنشاء فواتير المبيعات
5. **تسجيل المشتريات**: استخدم "فاتورة شراء" لتسجيل المشتريات وتحديث المخزون

### نصائح الاستخدام

- **البحث السريع**: يمكن البحث في المنتجات بالاسم أو الباركود في جميع النوافذ
- **النقر المزدوج**: انقر نقراً مزدوجاً على أي منتج لإضافته للفاتورة
- **تحديث المخزون**: يتم تحديث المخزون تلقائياً عند البيع أو الشراء
- **التنبيهات**: ستظهر تنبيهات في الشاشة الرئيسية عند انخفاض مخزون أي منتج

## الميزات المستقبلية

- 🔄 نظام صلاحيات المستخدمين المتقدم
- 🔄 دعم قارئ الباركود
- 🔄 ربط بآلة طباعة الفواتير الحرارية
- 🔄 إشعارات تلقائية للديون والتنبيهات
- 🔄 تطبيق ويب للوصول عن بُعد
- 🔄 تزامن البيانات مع السحابة
- 🔄 تحليلات متقدمة ولوحة معلومات تفاعلية

## الدعم والمساعدة

هذا النظام مطور للاستخدام المحلي في المحلات التجارية الصغيرة والمتوسطة. 

### المشاكل الشائعة

1. **خطأ في فتح قاعدة البيانات**: تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
2. **مشاكل في الخط العربي**: قد تحتاج لتثبيت خطوط عربية إضافية على النظام
3. **بطء في الأداء**: قم بعمل نسخة احتياطية وحذف البيانات القديمة غير المطلوبة

## الترخيص

هذا البرنامج مطور للاستخدام التعليمي والتجاري الحر.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025  
**الإصدار**: 1.0
