import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Product, Category
from ui_enhancements import ui_enhancements

class ProductsWindow:
    def __init__(self, parent):
        self.parent = parent
        self.product_model = Product()
        self.category_model = Category()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المنتجات")
        self.window.geometry("1000x700")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_products()
        self.load_categories()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=10, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المنتجات"""
        # إطار العنوان المحسن
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=70)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        # عنوان مع أيقونة
        title_content = tk.Frame(title_frame, bg='#2c3e50')
        title_content.pack(expand=True, fill='both')

        title_label = tk.Label(title_content, text="📦 إدارة المنتجات",
                              font=self.arabic_font_bold, fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=15)

        # معلومات سريعة
        info_label = tk.Label(title_content, text="إضافة وتعديل وإدارة المنتجات",
                             font=self.arabic_font, fg='#bdc3c7', bg='#2c3e50')
        info_label.pack(side='right', padx=20, pady=15)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار النموذج (يسار) - محسن
        form_frame = ui_enhancements.create_card_frame(main_frame, title="📝 بيانات المنتج")
        form_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        self.create_product_form(form_frame)
        
        # إطار القائمة (يمين) - محسن
        list_frame = ui_enhancements.create_card_frame(main_frame, title="📋 قائمة المنتجات")
        list_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        self.create_products_list(list_frame)
    
    def create_product_form(self, parent):
        """إنشاء نموذج إدخال المنتج"""
        # اسم المنتج
        tk.Label(parent, text="اسم المنتج:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        self.name_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # الباركود
        tk.Label(parent, text="الباركود:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='e', padx=5, pady=5)
        self.barcode_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.barcode_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # التصنيف
        tk.Label(parent, text="التصنيف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=2, column=0, sticky='e', padx=5, pady=5)
        self.category_combo = ttk.Combobox(parent, font=self.arabic_font, width=23, state='readonly')
        self.category_combo.grid(row=2, column=1, padx=5, pady=5)
        
        # سعر الشراء
        tk.Label(parent, text="سعر الشراء:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=3, column=0, sticky='e', padx=5, pady=5)
        self.purchase_price_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.purchase_price_entry.grid(row=3, column=1, padx=5, pady=5)
        
        # سعر البيع
        tk.Label(parent, text="سعر البيع:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=4, column=0, sticky='e', padx=5, pady=5)
        self.selling_price_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.selling_price_entry.grid(row=4, column=1, padx=5, pady=5)
        
        # الكمية
        tk.Label(parent, text="الكمية:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=5, column=0, sticky='e', padx=5, pady=5)
        self.quantity_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.quantity_entry.grid(row=5, column=1, padx=5, pady=5)
        
        # الحد الأدنى
        tk.Label(parent, text="الحد الأدنى:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=6, column=0, sticky='e', padx=5, pady=5)
        self.min_quantity_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.min_quantity_entry.grid(row=6, column=1, padx=5, pady=5)
        self.min_quantity_entry.insert(0, "5")
        
        # الوحدة
        tk.Label(parent, text="الوحدة:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=7, column=0, sticky='e', padx=5, pady=5)
        self.unit_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.unit_entry.grid(row=7, column=1, padx=5, pady=5)
        self.unit_entry.insert(0, "قطعة")
        
        # الوصف
        tk.Label(parent, text="الوصف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=8, column=0, sticky='ne', padx=5, pady=5)
        self.description_text = tk.Text(parent, font=self.arabic_font, width=25, height=3)
        self.description_text.grid(row=8, column=1, padx=5, pady=5)
        
        # الأزرار
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.grid(row=9, column=0, columnspan=2, pady=20)
        
        self.add_btn = ui_enhancements.create_styled_button(
            buttons_frame, "إضافة", style='success', icon='add',
            font=self.arabic_font, width=10, command=self.add_product
        )
        self.add_btn.pack(side='left', padx=5)

        self.update_btn = ui_enhancements.create_styled_button(
            buttons_frame, "تحديث", style='primary', icon='edit',
            font=self.arabic_font, width=10, command=self.update_product, state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)

        self.delete_btn = ui_enhancements.create_styled_button(
            buttons_frame, "حذف", style='danger', icon='delete',
            font=self.arabic_font, width=10, command=self.delete_product, state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)

        self.clear_btn = ui_enhancements.create_styled_button(
            buttons_frame, "مسح", style='secondary', icon='refresh',
            font=self.arabic_font, width=10, command=self.clear_form
        )
        self.clear_btn.pack(side='left', padx=5)
    
    def create_products_list(self, parent):
        """إنشاء قائمة المنتجات"""
        # إطار البحث المحسن
        search_frame, self.search_entry = ui_enhancements.create_search_box(
            parent, placeholder="🔍 البحث في المنتجات...", command=self.search_products
        )
        search_frame.pack(fill='x', padx=5, pady=5)

        # زر بحث متقدم
        advanced_search_btn = ui_enhancements.create_styled_button(
            search_frame, "بحث متقدم", style='secondary', icon='settings',
            font=self.arabic_font, command=self.advanced_search
        )
        advanced_search_btn.pack(side='right', padx=5)
        
        # جدول المنتجات
        columns = ('ID', 'الاسم', 'الباركود', 'التصنيف', 'سعر البيع', 'الكمية', 'الوحدة')
        self.products_tree = ttk.Treeview(parent, columns=columns, show='headings', height=20)
        
        # تكوين الأعمدة
        self.products_tree.heading('ID', text='ID')
        self.products_tree.heading('الاسم', text='الاسم')
        self.products_tree.heading('الباركود', text='الباركود')
        self.products_tree.heading('التصنيف', text='التصنيف')
        self.products_tree.heading('سعر البيع', text='سعر البيع')
        self.products_tree.heading('الكمية', text='الكمية')
        self.products_tree.heading('الوحدة', text='الوحدة')
        
        # تحديد عرض الأعمدة
        self.products_tree.column('ID', width=50)
        self.products_tree.column('الاسم', width=150)
        self.products_tree.column('الباركود', width=100)
        self.products_tree.column('التصنيف', width=100)
        self.products_tree.column('سعر البيع', width=80)
        self.products_tree.column('الكمية', width=60)
        self.products_tree.column('الوحدة', width=60)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.products_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # ربط حدث النقر على المنتج
        self.products_tree.bind('<ButtonRelease-1>', self.on_product_select)
        
        # متغير لحفظ ID المنتج المحدد
        self.selected_product_id = None
    
    def load_categories(self):
        """تحميل التصنيفات"""
        categories = self.category_model.get_all_categories()
        category_names = [cat[1] for cat in categories]  # cat[1] هو اسم التصنيف
        self.category_combo['values'] = category_names
        
        # حفظ معرفات التصنيفات
        self.categories_dict = {cat[1]: cat[0] for cat in categories}  # {name: id}
    
    def load_products(self):
        """تحميل المنتجات"""
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تحميل المنتجات
        products = self.product_model.get_all_products()
        for product in products:
            # تحديد لون الصف حسب المخزون
            tags = ()
            if product[6] <= product[7]:  # quantity <= min_quantity
                tags = ('low_stock',)
            
            self.products_tree.insert('', 'end', values=(
                product[0],  # ID
                product[1],  # name
                product[2] or '',  # barcode
                product[12] or '',  # category_name
                f"{product[5]:.2f}",  # selling_price
                product[6],  # quantity
                product[8]   # unit
            ), tags=tags)
        
        # تكوين ألوان الصفوف
        self.products_tree.tag_configure('low_stock', background='#ffcccc')
    
    def search_products(self, search_term=None):
        """البحث في المنتجات"""
        if search_term is None:
            search_term = self.search_entry.get()

        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # البحث في المنتجات
        if search_term:
            products = self.product_model.search_products(search_term)
        else:
            products = self.product_model.get_all_products()

        # عرض النتائج مع تحسينات بصرية
        for product in products:
            tags = ()
            if product[6] == 0:  # نفدت الكمية
                tags = ('out_of_stock',)
            elif product[6] <= product[7]:  # quantity <= min_quantity
                tags = ('low_stock',)

            self.products_tree.insert('', 'end', values=(
                product[0],  # ID
                product[1],  # name
                product[2] or '',  # barcode
                product[12] or '',  # category_name
                f"{product[5]:.2f}",  # selling_price
                product[6],  # quantity
                product[8]   # unit
            ), tags=tags)

        # تكوين ألوان الصفوف المحسنة
        self.products_tree.tag_configure('low_stock', background='#fff3e0')
        self.products_tree.tag_configure('out_of_stock', background='#ffebee')

    def advanced_search(self):
        """البحث المتقدم"""
        ui_enhancements.create_notification_toast(
            self.window, "🔍 ميزة البحث المتقدم قيد التطوير", 'info', 2000
        )
    
    def on_product_select(self, event):
        """عند تحديد منتج من القائمة"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_id = item['values'][0]
            
            # جلب بيانات المنتج
            product = self.product_model.get_product(product_id)
            if product:
                self.fill_form_with_product(product)
                self.selected_product_id = product_id
                
                # تفعيل أزرار التحديث والحذف
                self.update_btn.config(state='normal')
                self.delete_btn.config(state='normal')
                self.add_btn.config(text='إضافة')
    
    def fill_form_with_product(self, product):
        """ملء النموذج ببيانات المنتج"""
        self.clear_form()
        
        self.name_entry.insert(0, product[1])
        if product[2]:
            self.barcode_entry.insert(0, product[2])
        
        # تحديد التصنيف
        if product[3]:
            category_name = product[12]  # category_name من الاستعلام
            if category_name in self.category_combo['values']:
                self.category_combo.set(category_name)
        
        self.purchase_price_entry.insert(0, str(product[4]))
        self.selling_price_entry.insert(0, str(product[5]))
        self.quantity_entry.insert(0, str(product[6]))
        self.min_quantity_entry.delete(0, 'end')
        self.min_quantity_entry.insert(0, str(product[7]))
        self.unit_entry.delete(0, 'end')
        self.unit_entry.insert(0, product[8])
        if product[9]:
            self.description_text.insert('1.0', product[9])
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_entry.delete(0, 'end')
        self.barcode_entry.delete(0, 'end')
        self.category_combo.set('')
        self.purchase_price_entry.delete(0, 'end')
        self.selling_price_entry.delete(0, 'end')
        self.quantity_entry.delete(0, 'end')
        self.min_quantity_entry.delete(0, 'end')
        self.min_quantity_entry.insert(0, "5")
        self.unit_entry.delete(0, 'end')
        self.unit_entry.insert(0, "قطعة")
        self.description_text.delete('1.0', 'end')
        
        self.selected_product_id = None
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        self.add_btn.config(text='إضافة')
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم المنتج")
            return False
        
        try:
            float(self.purchase_price_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "سعر الشراء يجب أن يكون رقماً")
            return False
        
        try:
            float(self.selling_price_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "سعر البيع يجب أن يكون رقماً")
            return False
        
        try:
            int(self.quantity_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقماً صحيحاً")
            return False
        
        try:
            int(self.min_quantity_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "الحد الأدنى يجب أن يكون رقماً صحيحاً")
            return False
        
        return True
    
    def add_product(self):
        """إضافة منتج جديد"""
        if not self.validate_form():
            return
        
        # جمع البيانات
        name = self.name_entry.get().strip()
        barcode = self.barcode_entry.get().strip() or None
        category_name = self.category_combo.get()
        category_id = self.categories_dict.get(category_name) if category_name else None
        purchase_price = float(self.purchase_price_entry.get())
        selling_price = float(self.selling_price_entry.get())
        quantity = int(self.quantity_entry.get())
        min_quantity = int(self.min_quantity_entry.get())
        unit = self.unit_entry.get().strip()
        description = self.description_text.get('1.0', 'end').strip()
        
        # إضافة المنتج
        success, product_id, message = self.product_model.add_product(
            name, barcode, category_id, purchase_price, selling_price,
            quantity, min_quantity, unit, description
        )
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_products()
        else:
            messagebox.showerror("خطأ", message)
    
    def update_product(self):
        """تحديث المنتج"""
        if not self.selected_product_id:
            messagebox.showerror("خطأ", "يجب تحديد منتج للتحديث")
            return
        
        if not self.validate_form():
            return
        
        # جمع البيانات
        name = self.name_entry.get().strip()
        barcode = self.barcode_entry.get().strip() or None
        category_name = self.category_combo.get()
        category_id = self.categories_dict.get(category_name) if category_name else None
        purchase_price = float(self.purchase_price_entry.get())
        selling_price = float(self.selling_price_entry.get())
        quantity = int(self.quantity_entry.get())
        min_quantity = int(self.min_quantity_entry.get())
        unit = self.unit_entry.get().strip()
        description = self.description_text.get('1.0', 'end').strip()
        
        # تحديث المنتج
        success, message = self.product_model.update_product(
            self.selected_product_id, name, barcode, category_id, purchase_price, 
            selling_price, quantity, min_quantity, unit, description
        )
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_products()
        else:
            messagebox.showerror("خطأ", message)
    
    def delete_product(self):
        """حذف المنتج"""
        if not self.selected_product_id:
            messagebox.showerror("خطأ", "يجب تحديد منتج للحذف")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المنتج؟"):
            success, message = self.product_model.delete_product(self.selected_product_id)
            
            if success:
                messagebox.showinfo("نجح", message)
                self.clear_form()
                self.load_products()
            else:
                messagebox.showerror("خطأ", message)
