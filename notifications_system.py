#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام الإشعارات والتنبيهات
يدير التنبيهات للمخزون المنخفض، الديون المستحقة، والتذكيرات
"""

import tkinter as tk
from tkinter import ttk, font
from datetime import datetime, timedelta
from database import db

class NotificationSystem:
    def __init__(self):
        self.db = db
        self.notifications = []
        self.load_notifications()
    
    def load_notifications(self):
        """تحميل جميع الإشعارات"""
        self.notifications = []
        
        # إشعارات المخزون المنخفض
        self.check_low_stock()
        
        # إشعارات الديون المستحقة
        self.check_overdue_debts()
        
        # إشعارات المنتجات منتهية الصلاحية
        self.check_expired_products()
        
        # إشعارات المبيعات اليومية
        self.check_daily_sales()
    
    def check_low_stock(self):
        """فحص المنتجات منخفضة المخزون"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT name, quantity, min_quantity, unit
                FROM products 
                WHERE quantity <= min_quantity AND quantity > 0
                ORDER BY (quantity - min_quantity) ASC
            ''')
            
            low_stock_products = cursor.fetchall()
            
            for product in low_stock_products:
                self.notifications.append({
                    'type': 'warning',
                    'category': 'مخزون منخفض',
                    'title': f'مخزون منخفض: {product[0]}',
                    'message': f'الكمية الحالية: {product[1]} {product[3]}, الحد الأدنى: {product[2]} {product[3]}',
                    'priority': 'high' if product[1] == 0 else 'medium',
                    'timestamp': datetime.now(),
                    'action': 'view_product'
                })
            
            # إشعار عام إذا كان هناك منتجات منخفضة المخزون
            if low_stock_products:
                self.notifications.append({
                    'type': 'info',
                    'category': 'ملخص المخزون',
                    'title': f'تنبيه: {len(low_stock_products)} منتج منخفض المخزون',
                    'message': 'يرجى مراجعة المنتجات وإعادة التخزين',
                    'priority': 'medium',
                    'timestamp': datetime.now(),
                    'action': 'view_inventory'
                })
            
            conn.close()
        except Exception as e:
            print(f"خطأ في فحص المخزون المنخفض: {str(e)}")
    
    def check_overdue_debts(self):
        """فحص الديون المستحقة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # فحص ديون العملاء
            cursor.execute('''
                SELECT c.name, c.balance, c.phone
                FROM customers c
                WHERE c.balance > 0
                ORDER BY c.balance DESC
            ''')
            
            customer_debts = cursor.fetchall()
            total_customer_debt = sum(debt[1] for debt in customer_debts)
            
            if customer_debts:
                self.notifications.append({
                    'type': 'warning',
                    'category': 'ديون العملاء',
                    'title': f'إجمالي ديون العملاء: {total_customer_debt:.2f} جنيه',
                    'message': f'عدد العملاء المدينين: {len(customer_debts)}',
                    'priority': 'medium',
                    'timestamp': datetime.now(),
                    'action': 'view_customer_debts'
                })
            
            # فحص ديون الموردين
            cursor.execute('''
                SELECT s.name, s.balance, s.phone
                FROM suppliers s
                WHERE s.balance > 0
                ORDER BY s.balance DESC
            ''')
            
            supplier_debts = cursor.fetchall()
            total_supplier_debt = sum(debt[1] for debt in supplier_debts)
            
            if supplier_debts:
                self.notifications.append({
                    'type': 'info',
                    'category': 'ديون الموردين',
                    'title': f'إجمالي ديون الموردين: {total_supplier_debt:.2f} جنيه',
                    'message': f'عدد الموردين الدائنين: {len(supplier_debts)}',
                    'priority': 'low',
                    'timestamp': datetime.now(),
                    'action': 'view_supplier_debts'
                })
            
            conn.close()
        except Exception as e:
            print(f"خطأ في فحص الديون: {str(e)}")
    
    def check_expired_products(self):
        """فحص المنتجات منتهية الصلاحية (إذا كان هناك تاريخ انتهاء)"""
        # هذه الميزة تحتاج إضافة عمود تاريخ الانتهاء للمنتجات
        # سنتركها للتطوير المستقبلي
        pass
    
    def check_daily_sales(self):
        """فحص مبيعات اليوم"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            today = datetime.now().strftime('%Y-%m-%d')
            
            # حساب مبيعات اليوم
            cursor.execute('''
                SELECT COUNT(*), COALESCE(SUM(final_amount), 0)
                FROM sales 
                WHERE DATE(created_at) = ?
            ''', (today,))
            
            daily_sales = cursor.fetchone()
            sales_count = daily_sales[0]
            sales_amount = daily_sales[1]
            
            # حساب متوسط المبيعات الأسبوعية للمقارنة
            week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            cursor.execute('''
                SELECT AVG(daily_amount) FROM (
                    SELECT DATE(created_at) as sale_date, SUM(final_amount) as daily_amount
                    FROM sales 
                    WHERE DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                )
            ''', (week_ago, today))
            
            avg_result = cursor.fetchone()
            avg_daily_sales = avg_result[0] if avg_result[0] else 0
            
            # إنشاء إشعار مبيعات اليوم
            if sales_count > 0:
                performance = "ممتاز" if sales_amount > avg_daily_sales * 1.2 else \
                             "جيد" if sales_amount > avg_daily_sales * 0.8 else "ضعيف"
                
                self.notifications.append({
                    'type': 'success' if performance == 'ممتاز' else 'info',
                    'category': 'مبيعات اليوم',
                    'title': f'مبيعات اليوم: {sales_amount:.2f} جنيه',
                    'message': f'عدد الفواتير: {sales_count} - الأداء: {performance}',
                    'priority': 'low',
                    'timestamp': datetime.now(),
                    'action': 'view_daily_report'
                })
            else:
                self.notifications.append({
                    'type': 'warning',
                    'category': 'مبيعات اليوم',
                    'title': 'لا توجد مبيعات اليوم',
                    'message': 'لم يتم تسجيل أي مبيعات حتى الآن',
                    'priority': 'medium',
                    'timestamp': datetime.now(),
                    'action': 'open_pos'
                })
            
            conn.close()
        except Exception as e:
            print(f"خطأ في فحص المبيعات اليومية: {str(e)}")
    
    def get_notifications(self, category=None, priority=None):
        """الحصول على الإشعارات مع إمكانية التصفية"""
        filtered_notifications = self.notifications
        
        if category:
            filtered_notifications = [n for n in filtered_notifications if n['category'] == category]
        
        if priority:
            filtered_notifications = [n for n in filtered_notifications if n['priority'] == priority]
        
        # ترتيب حسب الأولوية والوقت
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        filtered_notifications.sort(
            key=lambda x: (priority_order.get(x['priority'], 0), x['timestamp']), 
            reverse=True
        )
        
        return filtered_notifications
    
    def get_notification_count(self, priority=None):
        """الحصول على عدد الإشعارات"""
        if priority:
            return len([n for n in self.notifications if n['priority'] == priority])
        return len(self.notifications)
    
    def mark_as_read(self, notification_id):
        """تمييز الإشعار كمقروء"""
        # يمكن تطوير هذه الميزة لحفظ حالة الإشعارات في قاعدة البيانات
        pass

class NotificationsWindow:
    def __init__(self, parent):
        self.parent = parent
        self.notification_system = NotificationSystem()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("الإشعارات والتنبيهات")
        self.window.geometry("800x600")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل الإشعارات
        self.load_notifications()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة الإشعارات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#e67e22', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="الإشعارات والتنبيهات", 
                              font=self.arabic_font_large, fg='white', bg='#e67e22')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار التصفية
        filter_frame = tk.LabelFrame(main_frame, text="تصفية الإشعارات", 
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        filter_frame.pack(fill='x', padx=5, pady=5)
        
        self.create_filter_panel(filter_frame)
        
        # إطار الإشعارات
        notifications_frame = tk.LabelFrame(main_frame, text="قائمة الإشعارات", 
                                           font=self.arabic_font_bold, bg='#f0f0f0')
        notifications_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.create_notifications_list(notifications_frame)
    
    def create_filter_panel(self, parent):
        """إنشاء لوحة التصفية"""
        inner_frame = tk.Frame(parent, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # تصفية حسب الفئة
        tk.Label(inner_frame, text="الفئة:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.category_combo = ttk.Combobox(inner_frame, font=self.arabic_font, width=15, state='readonly')
        self.category_combo['values'] = ['الكل', 'مخزون منخفض', 'ديون العملاء', 'ديون الموردين', 'مبيعات اليوم']
        self.category_combo.set('الكل')
        self.category_combo.pack(side='left', padx=10)
        
        # تصفية حسب الأولوية
        tk.Label(inner_frame, text="الأولوية:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.priority_combo = ttk.Combobox(inner_frame, font=self.arabic_font, width=10, state='readonly')
        self.priority_combo['values'] = ['الكل', 'عالية', 'متوسطة', 'منخفضة']
        self.priority_combo.set('الكل')
        self.priority_combo.pack(side='left', padx=10)
        
        # زر التطبيق
        tk.Button(inner_frame, text="تطبيق التصفية", font=self.arabic_font,
                 bg='#3498db', fg='white', command=self.apply_filter).pack(side='left', padx=20)
        
        # زر التحديث
        tk.Button(inner_frame, text="تحديث", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=self.refresh_notifications).pack(side='left', padx=5)
    
    def create_notifications_list(self, parent):
        """إنشاء قائمة الإشعارات"""
        # إطار للقائمة مع شريط التمرير
        list_frame = tk.Frame(parent, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إنشاء Canvas للتمرير
        self.canvas = tk.Canvas(list_frame, bg='#f0f0f0')
        self.scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#f0f0f0')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس بالتمرير
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """التمرير بعجلة الماوس"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def load_notifications(self):
        """تحميل وعرض الإشعارات"""
        # مسح الإشعارات الحالية
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # الحصول على الإشعارات
        notifications = self.notification_system.get_notifications()
        
        if not notifications:
            # رسالة عدم وجود إشعارات
            no_notifications_frame = tk.Frame(self.scrollable_frame, bg='#ecf0f1', relief='raised', bd=1)
            no_notifications_frame.pack(fill='x', padx=5, pady=5)
            
            tk.Label(no_notifications_frame, text="لا توجد إشعارات", 
                    font=self.arabic_font_bold, bg='#ecf0f1', fg='#7f8c8d').pack(pady=20)
            return
        
        # عرض الإشعارات
        for i, notification in enumerate(notifications):
            self.create_notification_widget(notification, i)
    
    def create_notification_widget(self, notification, index):
        """إنشاء عنصر إشعار"""
        # تحديد الألوان حسب النوع والأولوية
        colors = {
            'success': {'bg': '#d5f4e6', 'border': '#27ae60'},
            'info': {'bg': '#d6eaf8', 'border': '#3498db'},
            'warning': {'bg': '#fdeaa7', 'border': '#f39c12'},
            'error': {'bg': '#fadbd8', 'border': '#e74c3c'}
        }
        
        color_scheme = colors.get(notification['type'], colors['info'])
        
        # إطار الإشعار
        notification_frame = tk.Frame(self.scrollable_frame, bg=color_scheme['bg'], 
                                     relief='raised', bd=2)
        notification_frame.pack(fill='x', padx=5, pady=2)
        
        # إطار المحتوى
        content_frame = tk.Frame(notification_frame, bg=color_scheme['bg'])
        content_frame.pack(fill='x', padx=10, pady=8)
        
        # الصف الأول: العنوان والوقت
        header_frame = tk.Frame(content_frame, bg=color_scheme['bg'])
        header_frame.pack(fill='x')
        
        # العنوان
        title_label = tk.Label(header_frame, text=notification['title'], 
                              font=self.arabic_font_bold, bg=color_scheme['bg'],
                              fg='#2c3e50')
        title_label.pack(side='left')
        
        # الوقت
        time_str = notification['timestamp'].strftime('%H:%M')
        time_label = tk.Label(header_frame, text=time_str, 
                             font=self.arabic_font, bg=color_scheme['bg'],
                             fg='#7f8c8d')
        time_label.pack(side='right')
        
        # الصف الثاني: الفئة والأولوية
        meta_frame = tk.Frame(content_frame, bg=color_scheme['bg'])
        meta_frame.pack(fill='x', pady=(2, 0))
        
        # الفئة
        category_label = tk.Label(meta_frame, text=f"[{notification['category']}]", 
                                 font=self.arabic_font, bg=color_scheme['bg'],
                                 fg=color_scheme['border'])
        category_label.pack(side='left')
        
        # الأولوية
        priority_text = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}
        priority_label = tk.Label(meta_frame, text=f"الأولوية: {priority_text[notification['priority']]}", 
                                 font=self.arabic_font, bg=color_scheme['bg'],
                                 fg='#7f8c8d')
        priority_label.pack(side='right')
        
        # الصف الثالث: الرسالة
        message_label = tk.Label(content_frame, text=notification['message'], 
                                font=self.arabic_font, bg=color_scheme['bg'],
                                fg='#34495e', wraplength=700, justify='right')
        message_label.pack(fill='x', pady=(5, 0))
        
        # زر الإجراء (إذا كان متوفراً)
        if notification.get('action'):
            action_btn = tk.Button(content_frame, text="اتخاذ إجراء", 
                                  font=self.arabic_font, bg=color_scheme['border'], 
                                  fg='white', relief='flat',
                                  command=lambda: self.handle_action(notification['action']))
            action_btn.pack(side='right', pady=(5, 0))
    
    def handle_action(self, action):
        """التعامل مع إجراءات الإشعارات"""
        if action == 'view_product':
            # فتح نافذة إدارة المنتجات
            from products_window import ProductsWindow
            ProductsWindow(self.window)
        elif action == 'view_inventory':
            # فتح تقرير المخزون
            from reports_window import ReportsWindow
            reports_window = ReportsWindow(self.window)
            reports_window.report_type.set("inventory")
            reports_window.generate_report()
        elif action == 'open_pos':
            # فتح نقطة البيع
            from pos_window import POSWindow
            POSWindow(self.window)
        elif action == 'view_daily_report':
            # فتح تقرير المبيعات اليومية
            from reports_window import ReportsWindow
            reports_window = ReportsWindow(self.window)
            reports_window.report_type.set("sales")
            reports_window.set_period('today')
            reports_window.generate_report()
        else:
            tk.messagebox.showinfo("معلومات", f"الإجراء {action} قيد التطوير")
    
    def apply_filter(self):
        """تطبيق التصفية"""
        category = self.category_combo.get()
        priority = self.priority_combo.get()
        
        # تحويل القيم العربية إلى الإنجليزية
        category_map = {
            'الكل': None,
            'مخزون منخفض': 'مخزون منخفض',
            'ديون العملاء': 'ديون العملاء',
            'ديون الموردين': 'ديون الموردين',
            'مبيعات اليوم': 'مبيعات اليوم'
        }
        
        priority_map = {
            'الكل': None,
            'عالية': 'high',
            'متوسطة': 'medium',
            'منخفضة': 'low'
        }
        
        # تطبيق التصفية وإعادة التحميل
        filtered_category = category_map.get(category)
        filtered_priority = priority_map.get(priority)
        
        # مسح الإشعارات الحالية
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # الحصول على الإشعارات المصفاة
        notifications = self.notification_system.get_notifications(filtered_category, filtered_priority)
        
        # عرض الإشعارات المصفاة
        if not notifications:
            no_notifications_frame = tk.Frame(self.scrollable_frame, bg='#ecf0f1', relief='raised', bd=1)
            no_notifications_frame.pack(fill='x', padx=5, pady=5)
            
            tk.Label(no_notifications_frame, text="لا توجد إشعارات تطابق المعايير المحددة", 
                    font=self.arabic_font_bold, bg='#ecf0f1', fg='#7f8c8d').pack(pady=20)
        else:
            for i, notification in enumerate(notifications):
                self.create_notification_widget(notification, i)
    
    def refresh_notifications(self):
        """تحديث الإشعارات"""
        self.notification_system.load_notifications()
        self.load_notifications()

# إنشاء مثيل من نظام الإشعارات
notification_system = NotificationSystem()
