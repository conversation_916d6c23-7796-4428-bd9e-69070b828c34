#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة المخزون المتقدم
يدير تواريخ الانتهاء، الدفعات، والجرد الدوري
"""

import tkinter as tk
from tkinter import ttk, messagebox, font, filedialog
from datetime import datetime, timedelta
from database import db
import sqlite3

class AdvancedInventorySystem:
    def __init__(self):
        self.db = db
        self.setup_advanced_tables()

    def setup_advanced_tables(self):
        """إنشاء جداول المخزون المتقدم"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # جدول الدفعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_batches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    batch_number TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    purchase_price REAL NOT NULL,
                    expiry_date TEXT,
                    supplier_id INTEGER,
                    purchase_date TEXT NOT NULL,
                    notes TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')

            # جدول حركات المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    batch_id INTEGER,
                    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
                    quantity INTEGER NOT NULL,
                    reference_type TEXT,
                    reference_id INTEGER,
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (batch_id) REFERENCES product_batches (id)
                )
            ''')

            # جدول الجرد
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_counts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    count_date TEXT NOT NULL,
                    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'completed', 'cancelled')),
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول تفاصيل الجرد
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_count_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    count_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    batch_id INTEGER,
                    system_quantity INTEGER NOT NULL,
                    counted_quantity INTEGER,
                    difference INTEGER,
                    notes TEXT,
                    FOREIGN KEY (count_id) REFERENCES inventory_counts (id),
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (batch_id) REFERENCES product_batches (id)
                )
            ''')

            # جدول تحويلات المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_transfers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    from_location TEXT,
                    to_location TEXT,
                    transfer_date TEXT NOT NULL,
                    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول تفاصيل التحويلات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_transfer_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transfer_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    batch_id INTEGER,
                    quantity INTEGER NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (transfer_id) REFERENCES inventory_transfers (id),
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (batch_id) REFERENCES product_batches (id)
                )
            ''')

            conn.commit()
            conn.close()
            print("تم إنشاء جداول المخزون المتقدم بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء جداول المخزون المتقدم: {str(e)}")

    def add_batch(self, product_id, batch_number, quantity, purchase_price,
                  expiry_date=None, supplier_id=None, notes=None):
        """إضافة دفعة جديدة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            purchase_date = datetime.now().strftime('%Y-%m-%d')

            cursor.execute('''
                INSERT INTO product_batches
                (product_id, batch_number, quantity, purchase_price, expiry_date,
                 supplier_id, purchase_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (product_id, batch_number, quantity, purchase_price,
                  expiry_date, supplier_id, purchase_date, notes))

            batch_id = cursor.lastrowid

            # تسجيل حركة دخول للمخزون
            self.record_movement(cursor, product_id, batch_id, 'in', quantity,
                               'batch', batch_id, f"إضافة دفعة جديدة: {batch_number}")

            conn.commit()
            conn.close()

            return True, f"تم إضافة الدفعة بنجاح (ID: {batch_id})"

        except Exception as e:
            return False, f"خطأ في إضافة الدفعة: {str(e)}"

    def record_movement(self, cursor, product_id, batch_id, movement_type,
                       quantity, reference_type=None, reference_id=None, notes=None):
        """تسجيل حركة مخزون"""
        cursor.execute('''
            INSERT INTO inventory_movements
            (product_id, batch_id, movement_type, quantity, reference_type,
             reference_id, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (product_id, batch_id, movement_type, quantity,
              reference_type, reference_id, notes))

    def get_product_batches(self, product_id, active_only=True):
        """الحصول على دفعات المنتج"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            query = '''
                SELECT pb.*, s.name as supplier_name, p.name as product_name
                FROM product_batches pb
                LEFT JOIN suppliers s ON pb.supplier_id = s.id
                LEFT JOIN products p ON pb.product_id = p.id
                WHERE pb.product_id = ?
            '''

            if active_only:
                query += ' AND pb.is_active = 1 AND pb.quantity > 0'

            query += ' ORDER BY pb.expiry_date ASC, pb.purchase_date ASC'

            cursor.execute(query, (product_id,))
            batches = cursor.fetchall()

            conn.close()
            return batches

        except Exception as e:
            print(f"خطأ في جلب الدفعات: {str(e)}")
            return []

    def get_expiring_products(self, days_ahead=30):
        """الحصول على المنتجات منتهية الصلاحية"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            expiry_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')

            cursor.execute('''
                SELECT pb.*, p.name as product_name, s.name as supplier_name
                FROM product_batches pb
                JOIN products p ON pb.product_id = p.id
                LEFT JOIN suppliers s ON pb.supplier_id = s.id
                WHERE pb.expiry_date IS NOT NULL
                AND pb.expiry_date <= ?
                AND pb.is_active = 1
                AND pb.quantity > 0
                ORDER BY pb.expiry_date ASC
            ''', (expiry_date,))

            expiring_products = cursor.fetchall()
            conn.close()

            return expiring_products

        except Exception as e:
            print(f"خطأ في جلب المنتجات منتهية الصلاحية: {str(e)}")
            return []

    def start_inventory_count(self, notes=None):
        """بدء عملية جرد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            count_date = datetime.now().strftime('%Y-%m-%d')

            cursor.execute('''
                INSERT INTO inventory_counts (count_date, notes)
                VALUES (?, ?)
            ''', (count_date, notes))

            count_id = cursor.lastrowid

            # إضافة جميع المنتجات للجرد
            cursor.execute('''
                INSERT INTO inventory_count_items (count_id, product_id, system_quantity)
                SELECT ?, id, quantity FROM products WHERE quantity > 0
            ''', (count_id,))

            conn.commit()
            conn.close()

            return True, f"تم بدء الجرد بنجاح (ID: {count_id})"

        except Exception as e:
            return False, f"خطأ في بدء الجرد: {str(e)}"

    def update_count_item(self, count_id, product_id, counted_quantity, notes=None):
        """تحديث عنصر في الجرد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على الكمية في النظام
            cursor.execute('''
                SELECT system_quantity FROM inventory_count_items
                WHERE count_id = ? AND product_id = ?
            ''', (count_id, product_id))

            result = cursor.fetchone()
            if not result:
                return False, "عنصر الجرد غير موجود"

            system_quantity = result[0]
            difference = counted_quantity - system_quantity

            # تحديث عنصر الجرد
            cursor.execute('''
                UPDATE inventory_count_items
                SET counted_quantity = ?, difference = ?, notes = ?
                WHERE count_id = ? AND product_id = ?
            ''', (counted_quantity, difference, notes, count_id, product_id))

            conn.commit()
            conn.close()

            return True, "تم تحديث عنصر الجرد بنجاح"

        except Exception as e:
            return False, f"خطأ في تحديث عنصر الجرد: {str(e)}"

    def complete_inventory_count(self, count_id, apply_adjustments=True):
        """إكمال عملية الجرد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            if apply_adjustments:
                # تطبيق التعديلات على المخزون
                cursor.execute('''
                    SELECT product_id, difference FROM inventory_count_items
                    WHERE count_id = ? AND difference != 0
                ''', (count_id,))

                adjustments = cursor.fetchall()

                for product_id, difference in adjustments:
                    # تحديث كمية المنتج
                    cursor.execute('''
                        UPDATE products SET quantity = quantity + ?
                        WHERE id = ?
                    ''', (difference, product_id))

                    # تسجيل حركة تعديل
                    movement_type = 'in' if difference > 0 else 'out'
                    self.record_movement(cursor, product_id, None, 'adjustment',
                                       abs(difference), 'inventory_count', count_id,
                                       f"تعديل جرد: {difference}")

            # تحديث حالة الجرد
            cursor.execute('''
                UPDATE inventory_counts SET status = 'completed'
                WHERE id = ?
            ''', (count_id,))

            conn.commit()
            conn.close()

            return True, "تم إكمال الجرد بنجاح"

        except Exception as e:
            return False, f"خطأ في إكمال الجرد: {str(e)}"

    def get_inventory_movements(self, product_id=None, start_date=None, end_date=None):
        """الحصول على حركات المخزون"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            query = '''
                SELECT im.*, p.name as product_name, pb.batch_number
                FROM inventory_movements im
                JOIN products p ON im.product_id = p.id
                LEFT JOIN product_batches pb ON im.batch_id = pb.id
                WHERE 1=1
            '''
            params = []

            if product_id:
                query += ' AND im.product_id = ?'
                params.append(product_id)

            if start_date:
                query += ' AND DATE(im.created_at) >= ?'
                params.append(start_date)

            if end_date:
                query += ' AND DATE(im.created_at) <= ?'
                params.append(end_date)

            query += ' ORDER BY im.created_at DESC'

            cursor.execute(query, params)
            movements = cursor.fetchall()

            conn.close()
            return movements

        except Exception as e:
            print(f"خطأ في جلب حركات المخزون: {str(e)}")
            return []

class AdvancedInventoryWindow:
    def __init__(self, parent):
        self.parent = parent
        self.advanced_inventory = AdvancedInventorySystem()

        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المخزون المتقدم")
        self.window.geometry("1200x800")
        self.window.configure(bg='#f0f0f0')

        # تكوين الخط العربي
        self.setup_arabic_font()

        # إنشاء الواجهة
        self.create_interface()

    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")

    def create_interface(self):
        """إنشاء واجهة إدارة المخزون المتقدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="إدارة المخزون المتقدم",
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        # إطار رئيسي مع تبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # تبويب الدفعات
        batches_frame = tk.Frame(notebook, bg='#f0f0f0')
        notebook.add(batches_frame, text="إدارة الدفعات")
        self.create_batches_tab(batches_frame)

        # تبويب المنتجات منتهية الصلاحية
        expiry_frame = tk.Frame(notebook, bg='#f0f0f0')
        notebook.add(expiry_frame, text="منتهية الصلاحية")
        self.create_expiry_tab(expiry_frame)

        # تبويب الجرد
        count_frame = tk.Frame(notebook, bg='#f0f0f0')
        notebook.add(count_frame, text="الجرد")
        self.create_count_tab(count_frame)

        # تبويب حركات المخزون
        movements_frame = tk.Frame(notebook, bg='#f0f0f0')
        notebook.add(movements_frame, text="حركات المخزون")
        self.create_movements_tab(movements_frame)

    def create_batches_tab(self, parent):
        """إنشاء تبويب إدارة الدفعات"""
        # إطار إضافة دفعة جديدة
        add_frame = tk.LabelFrame(parent, text="إضافة دفعة جديدة",
                                 font=self.arabic_font_bold, bg='#f0f0f0')
        add_frame.pack(fill='x', padx=10, pady=5)

        # نموذج إضافة الدفعة
        form_frame = tk.Frame(add_frame, bg='#f0f0f0')
        form_frame.pack(fill='x', padx=10, pady=10)

        # الصف الأول
        row1 = tk.Frame(form_frame, bg='#f0f0f0')
        row1.pack(fill='x', pady=5)

        tk.Label(row1, text="المنتج:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.product_combo = ttk.Combobox(row1, font=self.arabic_font, width=25, state='readonly')
        self.product_combo.pack(side='left', padx=10)

        tk.Label(row1, text="رقم الدفعة:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.batch_number_entry = tk.Entry(row1, font=self.arabic_font, width=15)
        self.batch_number_entry.pack(side='left', padx=10)

        # الصف الثاني
        row2 = tk.Frame(form_frame, bg='#f0f0f0')
        row2.pack(fill='x', pady=5)

        tk.Label(row2, text="الكمية:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.quantity_entry = tk.Entry(row2, font=self.arabic_font, width=10)
        self.quantity_entry.pack(side='left', padx=10)

        tk.Label(row2, text="سعر الشراء:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.price_entry = tk.Entry(row2, font=self.arabic_font, width=10)
        self.price_entry.pack(side='left', padx=10)

        tk.Label(row2, text="تاريخ الانتهاء:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.expiry_entry = tk.Entry(row2, font=self.arabic_font, width=12)
        self.expiry_entry.pack(side='left', padx=10)
        self.expiry_entry.insert(0, "YYYY-MM-DD")

        # أزرار
        buttons_frame = tk.Frame(form_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=10)

        tk.Button(buttons_frame, text="إضافة دفعة", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=self.add_batch).pack(side='left', padx=5)

        tk.Button(buttons_frame, text="مسح النموذج", font=self.arabic_font,
                 bg='#95a5a6', fg='white', command=self.clear_batch_form).pack(side='left', padx=5)

        # قائمة الدفعات
        list_frame = tk.LabelFrame(parent, text="قائمة الدفعات",
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # جدول الدفعات
        columns = ('ID', 'المنتج', 'رقم الدفعة', 'الكمية', 'سعر الشراء', 'تاريخ الانتهاء', 'المورد')
        self.batches_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.batches_tree.heading(col, text=col)

        self.batches_tree.column('ID', width=50)
        self.batches_tree.column('المنتج', width=150)
        self.batches_tree.column('رقم الدفعة', width=100)
        self.batches_tree.column('الكمية', width=80)
        self.batches_tree.column('سعر الشراء', width=100)
        self.batches_tree.column('تاريخ الانتهاء', width=120)
        self.batches_tree.column('المورد', width=150)

        scrollbar_batches = ttk.Scrollbar(list_frame, orient='vertical', command=self.batches_tree.yview)
        self.batches_tree.configure(yscrollcommand=scrollbar_batches.set)

        self.batches_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_batches.pack(side='right', fill='y')

        # تحميل البيانات
        self.load_products_combo()
        self.load_batches()

    def create_expiry_tab(self, parent):
        """إنشاء تبويب المنتجات منتهية الصلاحية"""
        # إطار التحكم
        control_frame = tk.Frame(parent, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(control_frame, text="عرض المنتجات التي تنتهي خلال:",
                font=self.arabic_font, bg='#f0f0f0').pack(side='left')

        self.expiry_days_var = tk.StringVar(value="30")
        days_combo = ttk.Combobox(control_frame, textvariable=self.expiry_days_var,
                                 values=['7', '15', '30', '60', '90'], width=10, state='readonly')
        days_combo.pack(side='left', padx=10)

        tk.Label(control_frame, text="يوم", font=self.arabic_font, bg='#f0f0f0').pack(side='left')

        tk.Button(control_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', command=self.load_expiring_products).pack(side='left', padx=20)

        # جدول المنتجات منتهية الصلاحية
        expiry_frame = tk.LabelFrame(parent, text="المنتجات منتهية الصلاحية",
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        expiry_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('المنتج', 'رقم الدفعة', 'الكمية', 'تاريخ الانتهاء', 'الأيام المتبقية', 'المورد')
        self.expiry_tree = ttk.Treeview(expiry_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.expiry_tree.heading(col, text=col)

        self.expiry_tree.column('المنتج', width=150)
        self.expiry_tree.column('رقم الدفعة', width=100)
        self.expiry_tree.column('الكمية', width=80)
        self.expiry_tree.column('تاريخ الانتهاء', width=120)
        self.expiry_tree.column('الأيام المتبقية', width=100)
        self.expiry_tree.column('المورد', width=150)

        scrollbar_expiry = ttk.Scrollbar(expiry_frame, orient='vertical', command=self.expiry_tree.yview)
        self.expiry_tree.configure(yscrollcommand=scrollbar_expiry.set)

        self.expiry_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_expiry.pack(side='right', fill='y')

        # تحميل البيانات
        self.load_expiring_products()

    def create_count_tab(self, parent):
        """إنشاء تبويب الجرد"""
        # إطار التحكم
        control_frame = tk.Frame(parent, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=10, pady=5)

        tk.Button(control_frame, text="بدء جرد جديد", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=self.start_new_count).pack(side='left', padx=5)

        tk.Button(control_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', command=self.load_inventory_counts).pack(side='left', padx=5)

        # قائمة عمليات الجرد
        counts_frame = tk.LabelFrame(parent, text="عمليات الجرد",
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        counts_frame.pack(fill='both', expand=True, padx=10, pady=5)

        messagebox.showinfo("قريباً", "تبويب الجرد قيد التطوير")

    def create_movements_tab(self, parent):
        """إنشاء تبويب حركات المخزون"""
        # إطار التصفية
        filter_frame = tk.LabelFrame(parent, text="تصفية حركات المخزون",
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        filter_frame.pack(fill='x', padx=10, pady=5)

        messagebox.showinfo("قريباً", "تبويب حركات المخزون قيد التطوير")

    def load_products_combo(self):
        """تحميل قائمة المنتجات"""
        try:
            conn = self.advanced_inventory.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name FROM products ORDER BY name')
            products = cursor.fetchall()

            product_names = [f"{product[1]} (ID: {product[0]})" for product in products]
            self.product_combo['values'] = product_names

            # حفظ معرفات المنتجات
            self.products_dict = {f"{product[1]} (ID: {product[0]})": product[0] for product in products}

            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات: {str(e)}")

    def add_batch(self):
        """إضافة دفعة جديدة"""
        product_selection = self.product_combo.get()
        batch_number = self.batch_number_entry.get().strip()
        quantity = self.quantity_entry.get().strip()
        price = self.price_entry.get().strip()
        expiry_date = self.expiry_entry.get().strip()

        if not all([product_selection, batch_number, quantity, price]):
            messagebox.showerror("خطأ", "يجب ملء جميع الحقول المطلوبة")
            return

        try:
            product_id = self.products_dict.get(product_selection)
            quantity = int(quantity)
            price = float(price)

            # التحقق من صحة تاريخ الانتهاء
            if expiry_date and expiry_date != "YYYY-MM-DD":
                try:
                    datetime.strptime(expiry_date, '%Y-%m-%d')
                except ValueError:
                    messagebox.showerror("خطأ", "تاريخ الانتهاء غير صحيح. استخدم تنسيق YYYY-MM-DD")
                    return
            else:
                expiry_date = None

            success, message = self.advanced_inventory.add_batch(
                product_id, batch_number, quantity, price, expiry_date
            )

            if success:
                messagebox.showinfo("نجح", message)
                self.clear_batch_form()
                self.load_batches()
            else:
                messagebox.showerror("خطأ", message)

        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال قيم صحيحة للكمية والسعر")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الدفعة: {str(e)}")

    def clear_batch_form(self):
        """مسح نموذج الدفعة"""
        self.product_combo.set('')
        self.batch_number_entry.delete(0, 'end')
        self.quantity_entry.delete(0, 'end')
        self.price_entry.delete(0, 'end')
        self.expiry_entry.delete(0, 'end')
        self.expiry_entry.insert(0, "YYYY-MM-DD")

    def load_batches(self):
        """تحميل قائمة الدفعات"""
        # مسح الجدول
        for item in self.batches_tree.get_children():
            self.batches_tree.delete(item)

        try:
            conn = self.advanced_inventory.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT pb.id, p.name, pb.batch_number, pb.quantity,
                       pb.purchase_price, pb.expiry_date, s.name
                FROM product_batches pb
                JOIN products p ON pb.product_id = p.id
                LEFT JOIN suppliers s ON pb.supplier_id = s.id
                WHERE pb.is_active = 1
                ORDER BY pb.created_at DESC
            ''')

            batches = cursor.fetchall()

            for batch in batches:
                expiry_date = batch[5] if batch[5] else 'غير محدد'
                supplier_name = batch[6] if batch[6] else 'غير محدد'

                self.batches_tree.insert('', 'end', values=(
                    batch[0],  # ID
                    batch[1],  # اسم المنتج
                    batch[2],  # رقم الدفعة
                    batch[3],  # الكمية
                    f"{batch[4]:.2f}",  # سعر الشراء
                    expiry_date,  # تاريخ الانتهاء
                    supplier_name  # اسم المورد
                ))

            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الدفعات: {str(e)}")

    def load_expiring_products(self):
        """تحميل المنتجات منتهية الصلاحية"""
        # مسح الجدول
        for item in self.expiry_tree.get_children():
            self.expiry_tree.delete(item)

        try:
            days_ahead = int(self.expiry_days_var.get())
            expiring_products = self.advanced_inventory.get_expiring_products(days_ahead)

            for product in expiring_products:
                # حساب الأيام المتبقية
                expiry_date = datetime.strptime(product[5], '%Y-%m-%d')
                days_remaining = (expiry_date - datetime.now()).days

                # تحديد لون الصف حسب الأيام المتبقية
                if days_remaining < 0:
                    tags = ('expired',)
                elif days_remaining <= 7:
                    tags = ('critical',)
                elif days_remaining <= 30:
                    tags = ('warning',)
                else:
                    tags = ()

                supplier_name = product[12] if product[12] else 'غير محدد'

                item = self.expiry_tree.insert('', 'end', values=(
                    product[11],  # اسم المنتج
                    product[2],   # رقم الدفعة
                    product[3],   # الكمية
                    product[5],   # تاريخ الانتهاء
                    f"{days_remaining} يوم",  # الأيام المتبقية
                    supplier_name  # اسم المورد
                ), tags=tags)

            # تكوين ألوان الصفوف
            self.expiry_tree.tag_configure('expired', background='#ffebee')
            self.expiry_tree.tag_configure('critical', background='#fff3e0')
            self.expiry_tree.tag_configure('warning', background='#f3e5f5')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات منتهية الصلاحية: {str(e)}")

    def start_new_count(self):
        """بدء جرد جديد"""
        notes = tk.simpledialog.askstring("ملاحظات الجرد", "أدخل ملاحظات للجرد (اختياري):")

        success, message = self.advanced_inventory.start_inventory_count(notes)

        if success:
            messagebox.showinfo("نجح", message)
            self.load_inventory_counts()
        else:
            messagebox.showerror("خطأ", message)

    def load_inventory_counts(self):
        """تحميل عمليات الجرد"""
        messagebox.showinfo("قريباً", "تحميل عمليات الجرد قيد التطوير")

# إنشاء مثيل من نظام المخزون المتقدم
advanced_inventory = AdvancedInventorySystem()