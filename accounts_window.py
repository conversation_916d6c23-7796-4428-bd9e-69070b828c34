import tkinter as tk
from tkinter import ttk, messagebox, font
from datetime import datetime
from database import db

class AccountsWindow:
    def __init__(self, parent, account_type='customer', account_id=None):
        self.parent = parent
        self.db = db
        self.account_type = account_type  # 'customer' or 'supplier'
        self.account_id = account_id
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title(f"كشف حساب {'العميل' if account_type == 'customer' else 'المورد'}")
        self.window.geometry("1000x700")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        if account_id:
            self.load_account_statement()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة كشف الحساب"""
        # إطار العنوان
        title_color = '#2980b9' if self.account_type == 'customer' else '#e67e22'
        title_frame = tk.Frame(self.window, bg=title_color, height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_text = f"كشف حساب {'العميل' if self.account_type == 'customer' else 'المورد'}"
        title_label = tk.Label(title_frame, text=title_text, 
                              font=self.arabic_font_large, fg='white', bg=title_color)
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار اختيار الحساب
        selection_frame = tk.LabelFrame(main_frame, text="اختيار الحساب", 
                                       font=self.arabic_font_bold, bg='#f0f0f0')
        selection_frame.pack(fill='x', padx=5, pady=5)
        
        self.create_selection_panel(selection_frame)
        
        # إطار معلومات الحساب
        info_frame = tk.LabelFrame(main_frame, text="معلومات الحساب", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        info_frame.pack(fill='x', padx=5, pady=5)
        
        self.create_info_panel(info_frame)
        
        # إطار كشف الحساب
        statement_frame = tk.LabelFrame(main_frame, text="كشف الحساب", 
                                       font=self.arabic_font_bold, bg='#f0f0f0')
        statement_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.create_statement_panel(statement_frame)
    
    def create_selection_panel(self, parent):
        """إنشاء لوحة اختيار الحساب"""
        inner_frame = tk.Frame(parent, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # اختيار الحساب
        account_label = f"اختر {'العميل' if self.account_type == 'customer' else 'المورد'}:"
        tk.Label(inner_frame, text=account_label, font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        
        self.account_combo = ttk.Combobox(inner_frame, font=self.arabic_font, width=30, state='readonly')
        self.account_combo.pack(side='left', padx=10)
        self.account_combo.bind('<<ComboboxSelected>>', self.on_account_select)
        
        # زر تحديث
        tk.Button(inner_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', command=self.load_account_statement).pack(side='left', padx=10)
        
        # زر طباعة
        tk.Button(inner_frame, text="طباعة", font=self.arabic_font,
                 bg='#e74c3c', fg='white', command=self.print_statement).pack(side='left', padx=5)
        
        # تحميل قائمة الحسابات
        self.load_accounts()
    
    def create_info_panel(self, parent):
        """إنشاء لوحة معلومات الحساب"""
        inner_frame = tk.Frame(parent, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول
        row1_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=5)
        
        # اسم الحساب
        tk.Label(row1_frame, text="الاسم:", font=self.arabic_font_bold, bg='#f0f0f0').pack(side='left')
        self.account_name_label = tk.Label(row1_frame, text="", font=self.arabic_font, bg='#f0f0f0')
        self.account_name_label.pack(side='left', padx=10)
        
        # الهاتف
        tk.Label(row1_frame, text="الهاتف:", font=self.arabic_font_bold, bg='#f0f0f0').pack(side='left', padx=(30, 0))
        self.account_phone_label = tk.Label(row1_frame, text="", font=self.arabic_font, bg='#f0f0f0')
        self.account_phone_label.pack(side='left', padx=10)
        
        # الصف الثاني
        row2_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=5)
        
        # الرصيد الحالي
        tk.Label(row2_frame, text="الرصيد الحالي:", font=self.arabic_font_bold, bg='#f0f0f0').pack(side='left')
        self.current_balance_label = tk.Label(row2_frame, text="0.00", font=self.arabic_font_bold, 
                                             bg='#f0f0f0', fg='blue')
        self.current_balance_label.pack(side='left', padx=10)
        
        # إجمالي المعاملات
        tk.Label(row2_frame, text="إجمالي المعاملات:", font=self.arabic_font_bold, bg='#f0f0f0').pack(side='left', padx=(30, 0))
        self.total_transactions_label = tk.Label(row2_frame, text="0.00", font=self.arabic_font, bg='#f0f0f0')
        self.total_transactions_label.pack(side='left', padx=10)
    
    def create_statement_panel(self, parent):
        """إنشاء لوحة كشف الحساب"""
        # إطار الفترة الزمنية
        period_frame = tk.Frame(parent, bg='#f0f0f0')
        period_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(period_frame, text="من تاريخ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.start_date_entry = tk.Entry(period_frame, font=self.arabic_font, width=12)
        self.start_date_entry.pack(side='left', padx=5)
        self.start_date_entry.insert(0, "2024-01-01")
        
        tk.Label(period_frame, text="إلى تاريخ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.end_date_entry = tk.Entry(period_frame, font=self.arabic_font, width=12)
        self.end_date_entry.pack(side='left', padx=5)
        self.end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        tk.Button(period_frame, text="تطبيق الفترة", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=self.load_account_statement).pack(side='left', padx=20)
        
        # جدول كشف الحساب
        columns = ('التاريخ', 'النوع', 'رقم الفاتورة', 'البيان', 'مدين', 'دائن', 'الرصيد')
        self.statement_tree = ttk.Treeview(parent, columns=columns, show='headings', height=20)
        
        # تكوين الأعمدة
        for col in columns:
            self.statement_tree.heading(col, text=col)
        
        self.statement_tree.column('التاريخ', width=100)
        self.statement_tree.column('النوع', width=80)
        self.statement_tree.column('رقم الفاتورة', width=120)
        self.statement_tree.column('البيان', width=200)
        self.statement_tree.column('مدين', width=100)
        self.statement_tree.column('دائن', width=100)
        self.statement_tree.column('الرصيد', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.statement_tree.yview)
        self.statement_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.statement_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            if self.account_type == 'customer':
                cursor.execute('SELECT id, name FROM customers ORDER BY name')
            else:
                cursor.execute('SELECT id, name FROM suppliers ORDER BY name')
            
            accounts = cursor.fetchall()
            
            # تحديث القائمة
            account_names = [f"{account[1]} (ID: {account[0]})" for account in accounts]
            self.account_combo['values'] = account_names
            
            # حفظ معرفات الحسابات
            self.accounts_dict = {f"{account[1]} (ID: {account[0]})": account[0] for account in accounts}
            
            # تحديد الحساب إذا كان محدد مسبقاً
            if self.account_id:
                for account in accounts:
                    if account[0] == self.account_id:
                        self.account_combo.set(f"{account[1]} (ID: {account[0]})")
                        break
            
            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الحسابات: {str(e)}")
    
    def on_account_select(self, event=None):
        """عند اختيار حساب"""
        selected = self.account_combo.get()
        if selected:
            self.account_id = self.accounts_dict.get(selected)
            self.load_account_statement()
    
    def load_account_statement(self):
        """تحميل كشف الحساب"""
        if not self.account_id:
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # جلب معلومات الحساب
            if self.account_type == 'customer':
                cursor.execute('SELECT name, phone, balance FROM customers WHERE id = ?', (self.account_id,))
            else:
                cursor.execute('SELECT name, phone, balance FROM suppliers WHERE id = ?', (self.account_id,))
            
            account_info = cursor.fetchone()
            if not account_info:
                messagebox.showerror("خطأ", "الحساب غير موجود")
                return
            
            # تحديث معلومات الحساب
            self.account_name_label.config(text=account_info[0])
            self.account_phone_label.config(text=account_info[1] or 'غير محدد')
            self.current_balance_label.config(text=f"{account_info[2]:.2f}")
            
            # مسح الجدول
            for item in self.statement_tree.get_children():
                self.statement_tree.delete(item)
            
            # جلب المعاملات
            start_date = self.start_date_entry.get()
            end_date = self.end_date_entry.get()
            
            transactions = []
            
            if self.account_type == 'customer':
                # فواتير المبيعات
                cursor.execute('''
                    SELECT created_at, 'مبيعات' as type, invoice_number, 
                           'فاتورة مبيعات' as description, final_amount, 0
                    FROM sales 
                    WHERE customer_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    ORDER BY created_at
                ''', (self.account_id, start_date, end_date))
                
                sales = cursor.fetchall()
                transactions.extend(sales)
                
            else:
                # فواتير المشتريات
                cursor.execute('''
                    SELECT created_at, 'مشتريات' as type, invoice_number, 
                           'فاتورة مشتريات' as description, 0, final_amount
                    FROM purchases 
                    WHERE supplier_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    ORDER BY created_at
                ''', (self.account_id, start_date, end_date))
                
                purchases = cursor.fetchall()
                transactions.extend(purchases)
            
            # ترتيب المعاملات حسب التاريخ
            transactions.sort(key=lambda x: x[0])
            
            # حساب الرصيد التراكمي وعرض المعاملات
            running_balance = 0.0
            total_debit = 0.0
            total_credit = 0.0
            
            for transaction in transactions:
                date = transaction[0][:10]  # التاريخ فقط
                trans_type = transaction[1]
                invoice_number = transaction[2]
                description = transaction[3]
                debit = transaction[4]  # مدين
                credit = transaction[5]  # دائن
                
                # حساب الرصيد التراكمي
                if self.account_type == 'customer':
                    running_balance += debit - credit
                else:
                    running_balance += credit - debit
                
                total_debit += debit
                total_credit += credit
                
                # إضافة الصف
                self.statement_tree.insert('', 'end', values=(
                    date,
                    trans_type,
                    invoice_number,
                    description,
                    f"{debit:.2f}" if debit > 0 else "",
                    f"{credit:.2f}" if credit > 0 else "",
                    f"{running_balance:.2f}"
                ))
            
            # إضافة صف الإجمالي
            if transactions:
                self.statement_tree.insert('', 'end', values=(
                    "الإجمالي",
                    "",
                    "",
                    "",
                    f"{total_debit:.2f}",
                    f"{total_credit:.2f}",
                    f"{running_balance:.2f}"
                ), tags=('total',))
                
                # تنسيق صف الإجمالي
                self.statement_tree.tag_configure('total', background='#d5dbdb', font=self.arabic_font_bold)
            
            # تحديث إجمالي المعاملات
            self.total_transactions_label.config(text=f"{total_debit + total_credit:.2f}")
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل كشف الحساب: {str(e)}")
    
    def print_statement(self):
        """طباعة كشف الحساب"""
        if not self.account_id:
            messagebox.showerror("خطأ", "يجب اختيار حساب أولاً")
            return
        
        try:
            from pdf_generator import pdf_generator
            from tkinter import filedialog
            
            # تحضير بيانات كشف الحساب
            account_name = self.account_name_label.cget("text")
            account_phone = self.account_phone_label.cget("text")
            current_balance = self.current_balance_label.cget("text")
            
            # جمع بيانات المعاملات
            transactions = []
            for item in self.statement_tree.get_children():
                values = self.statement_tree.item(item)['values']
                if values[0] != "الإجمالي":  # تجاهل صف الإجمالي
                    transactions.append(values)
            
            # إعداد بيانات التقرير
            report_data = {
                'type': 'account_statement',
                'period': f"من {self.start_date_entry.get()} إلى {self.end_date_entry.get()}",
                'summary': f"""
كشف حساب {'العميل' if self.account_type == 'customer' else 'المورد'}: {account_name}
الهاتف: {account_phone}
الرصيد الحالي: {current_balance} جنيه
عدد المعاملات: {len(transactions)}
                """,
                'data': transactions,
                'columns': ['التاريخ', 'النوع', 'رقم الفاتورة', 'البيان', 'مدين', 'دائن', 'الرصيد']
            }
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ كشف الحساب"
            )
            
            if filename:
                # إنشاء ملف PDF
                saved_file = pdf_generator.generate_report_pdf(report_data, filename)
                messagebox.showinfo("نجح", f"تم حفظ كشف الحساب في:\n{saved_file}")
                
                # فتح الملف
                import os
                os.startfile(saved_file)
        
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة ReportLab غير مثبتة\nقم بتثبيتها باستخدام: pip install reportlab")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء كشف الحساب: {str(e)}")
