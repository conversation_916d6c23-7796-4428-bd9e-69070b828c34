#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
لوحة المعلومات الرئيسية (Dashboard)
عرض الإحصائيات والمخططات البيانية
"""

import tkinter as tk
from tkinter import ttk, font
from datetime import datetime, timedelta
import sqlite3
from database import db
from ui_enhancements import ui_enhancements
# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    # تكوين matplotlib للعربية
    rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر - سيتم عرض لوحة المعلومات بدون مخططات بيانية")

class Dashboard:
    def __init__(self, parent):
        self.parent = parent
        self.db = db
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("لوحة المعلومات - Dashboard")
        self.window.geometry("1400x900")
        self.window.configure(bg='#f8f9fa')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_dashboard_data()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=16, weight="bold")
            self.arabic_font_small = font.Font(family="Arial Unicode MS", size=9)
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=16, weight="bold")
            self.arabic_font_small = font.Font(size=9)
    
    def create_interface(self):
        """إنشاء واجهة لوحة المعلومات"""
        # إطار العنوان
        self.create_header()
        
        # إطار رئيسي مع تمرير
        main_canvas = tk.Canvas(self.window, bg='#f8f9fa')
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=main_canvas.yview)
        self.scrollable_frame = tk.Frame(main_canvas, bg='#f8f9fa')
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        main_canvas.pack(side="left", fill="both", expand=True, padx=10, pady=5)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        main_canvas.bind_all("<MouseWheel>", lambda e: main_canvas.yview_scroll(int(-1*(e.delta/120)), "units"))
        
        # إنشاء أقسام لوحة المعلومات
        self.create_summary_cards()
        self.create_charts_section()
        self.create_recent_activities()
        self.create_alerts_section()
    
    def create_header(self):
        """إنشاء رأس لوحة المعلومات"""
        header_frame = tk.Frame(self.window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        # العنوان والوقت
        title_frame = tk.Frame(header_frame, bg='#2c3e50')
        title_frame.pack(expand=True, fill='both')
        
        # العنوان الرئيسي
        title_label = tk.Label(title_frame, text="📊 لوحة المعلومات الرئيسية", 
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=20)
        
        # الوقت والتاريخ
        current_time = datetime.now()
        time_text = f"📅 {current_time.strftime('%Y-%m-%d')} | 🕐 {current_time.strftime('%H:%M')}"
        time_label = tk.Label(title_frame, text=time_text, 
                             font=self.arabic_font, fg='#bdc3c7', bg='#2c3e50')
        time_label.pack(side='right', padx=20, pady=20)
        
        # زر التحديث
        refresh_btn = ui_enhancements.create_styled_button(
            title_frame, "تحديث", style='success', icon='refresh',
            font=self.arabic_font, command=self.refresh_dashboard
        )
        refresh_btn.pack(side='right', padx=10, pady=20)
    
    def create_summary_cards(self):
        """إنشاء بطاقات الملخص"""
        cards_frame = tk.Frame(self.scrollable_frame, bg='#f8f9fa')
        cards_frame.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول من البطاقات
        row1_frame = tk.Frame(cards_frame, bg='#f8f9fa')
        row1_frame.pack(fill='x', pady=5)
        
        # بطاقة المبيعات اليومية
        self.sales_card = self.create_summary_card(
            row1_frame, "💰 مبيعات اليوم", "0.00 جنيه", "#27ae60", "left"
        )
        
        # بطاقة عدد الفواتير
        self.invoices_card = self.create_summary_card(
            row1_frame, "🧾 عدد الفواتير", "0 فاتورة", "#3498db", "left"
        )
        
        # بطاقة المنتجات منخفضة المخزون
        self.low_stock_card = self.create_summary_card(
            row1_frame, "⚠️ مخزون منخفض", "0 منتج", "#e74c3c", "left"
        )
        
        # بطاقة إجمالي العملاء
        self.customers_card = self.create_summary_card(
            row1_frame, "👥 إجمالي العملاء", "0 عميل", "#9b59b6", "left"
        )
        
        # الصف الثاني من البطاقات
        row2_frame = tk.Frame(cards_frame, bg='#f8f9fa')
        row2_frame.pack(fill='x', pady=5)
        
        # بطاقة الأرباح الشهرية
        self.profit_card = self.create_summary_card(
            row2_frame, "📈 أرباح الشهر", "0.00 جنيه", "#f39c12", "left"
        )
        
        # بطاقة المشتريات الشهرية
        self.purchases_card = self.create_summary_card(
            row2_frame, "📦 مشتريات الشهر", "0.00 جنيه", "#e67e22", "left"
        )
        
        # بطاقة ديون العملاء
        self.debts_card = self.create_summary_card(
            row2_frame, "💳 ديون العملاء", "0.00 جنيه", "#e74c3c", "left"
        )
        
        # بطاقة إجمالي المنتجات
        self.products_card = self.create_summary_card(
            row2_frame, "📦 إجمالي المنتجات", "0 منتج", "#34495e", "left"
        )
    
    def create_summary_card(self, parent, title, value, color, side):
        """إنشاء بطاقة ملخص"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.pack(side=side, fill='both', expand=True, padx=5, pady=5)
        
        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=color)
        content_frame.pack(expand=True, fill='both', padx=15, pady=15)
        
        # العنوان
        title_label = tk.Label(content_frame, text=title, 
                              font=self.arabic_font_bold, fg='white', bg=color)
        title_label.pack(anchor='w')
        
        # القيمة
        value_label = tk.Label(content_frame, text=value, 
                              font=self.arabic_font_large, fg='white', bg=color)
        value_label.pack(anchor='w', pady=(5, 0))
        
        return value_label
    
    def create_charts_section(self):
        """إنشاء قسم المخططات البيانية"""
        charts_frame = tk.Frame(self.scrollable_frame, bg='#f8f9fa')
        charts_frame.pack(fill='x', padx=10, pady=10)
        
        # عنوان القسم
        section_title = tk.Label(charts_frame, text="📊 المخططات البيانية", 
                                font=self.arabic_font_large, fg='#2c3e50', bg='#f8f9fa')
        section_title.pack(anchor='w', pady=(0, 10))
        
        # إطار المخططات
        charts_container = tk.Frame(charts_frame, bg='#f8f9fa')
        charts_container.pack(fill='x')
        
        if MATPLOTLIB_AVAILABLE:
            # مخطط المبيعات الأسبوعية
            self.create_sales_chart(charts_container)

            # مخطط أفضل المنتجات
            self.create_top_products_chart(charts_container)
        else:
            # عرض بديل بدون مخططات
            self.create_simple_charts(charts_container)
    
    def create_sales_chart(self, parent):
        """إنشاء مخطط المبيعات الأسبوعية"""
        chart_frame = ui_enhancements.create_card_frame(parent, title="📈 مبيعات آخر 7 أيام")
        chart_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        # إنشاء المخطط
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('#ffffff')
        
        # بيانات وهمية (سيتم تحديثها من قاعدة البيانات)
        dates = [(datetime.now() - timedelta(days=i)).strftime('%m-%d') for i in range(6, -1, -1)]
        sales = [0] * 7  # سيتم تحديثها
        
        ax.plot(dates, sales, marker='o', linewidth=2, markersize=6, color='#27ae60')
        ax.fill_between(dates, sales, alpha=0.3, color='#27ae60')
        
        ax.set_title('مبيعات آخر 7 أيام', fontsize=12, pad=20)
        ax.set_xlabel('التاريخ', fontsize=10)
        ax.set_ylabel('المبيعات (جنيه)', fontsize=10)
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة المخطط للواجهة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
        
        self.sales_chart = (fig, ax, canvas)
    
    def create_top_products_chart(self, parent):
        """إنشاء مخطط أفضل المنتجات"""
        chart_frame = ui_enhancements.create_card_frame(parent, title="🏆 أفضل 5 منتجات مبيعاً")
        chart_frame.pack(side='right', fill='both', expand=True, padx=5)
        
        # إنشاء المخطط
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('#ffffff')
        
        # بيانات وهمية (سيتم تحديثها من قاعدة البيانات)
        products = ['منتج 1', 'منتج 2', 'منتج 3', 'منتج 4', 'منتج 5']
        quantities = [0, 0, 0, 0, 0]  # سيتم تحديثها
        
        colors = ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6']
        bars = ax.bar(products, quantities, color=colors)
        
        ax.set_title('أفضل 5 منتجات مبيعاً', fontsize=12, pad=20)
        ax.set_xlabel('المنتجات', fontsize=10)
        ax.set_ylabel('الكمية المباعة', fontsize=10)
        
        # إضافة قيم على الأعمدة
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة المخطط للواجهة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
        
        self.products_chart = (fig, ax, canvas)

    def create_simple_charts(self, parent):
        """إنشاء مخططات بسيطة بدون matplotlib"""
        # مخطط المبيعات البسيط
        sales_frame = ui_enhancements.create_card_frame(parent, title="📈 مبيعات آخر 7 أيام")
        sales_frame.pack(side='left', fill='both', expand=True, padx=5)

        # جدول المبيعات
        sales_data_frame = tk.Frame(sales_frame, bg='white')
        sales_data_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # عناوين الجدول
        headers = ['التاريخ', 'المبيعات (جنيه)']
        for i, header in enumerate(headers):
            header_label = tk.Label(sales_data_frame, text=header,
                                   font=self.arabic_font_bold, bg='#f8f9fa',
                                   relief='solid', bd=1)
            header_label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)

        # بيانات المبيعات (سيتم تحديثها)
        self.sales_data_labels = []
        for day in range(7):
            date_label = tk.Label(sales_data_frame, text=f"يوم {day+1}",
                                 font=self.arabic_font, bg='white',
                                 relief='solid', bd=1)
            date_label.grid(row=day+1, column=0, sticky='ew', padx=1, pady=1)

            amount_label = tk.Label(sales_data_frame, text="0.00",
                                   font=self.arabic_font, bg='white',
                                   relief='solid', bd=1)
            amount_label.grid(row=day+1, column=1, sticky='ew', padx=1, pady=1)

            self.sales_data_labels.append((date_label, amount_label))

        # تكوين الأعمدة
        sales_data_frame.grid_columnconfigure(0, weight=1)
        sales_data_frame.grid_columnconfigure(1, weight=1)

        # مخطط المنتجات البسيط
        products_frame = ui_enhancements.create_card_frame(parent, title="🏆 أفضل 5 منتجات مبيعاً")
        products_frame.pack(side='right', fill='both', expand=True, padx=5)

        # قائمة المنتجات
        products_data_frame = tk.Frame(products_frame, bg='white')
        products_data_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # عناوين قائمة المنتجات
        product_headers = ['المنتج', 'الكمية المباعة']
        for i, header in enumerate(product_headers):
            header_label = tk.Label(products_data_frame, text=header,
                                   font=self.arabic_font_bold, bg='#f8f9fa',
                                   relief='solid', bd=1)
            header_label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)

        # بيانات المنتجات (سيتم تحديثها)
        self.products_data_labels = []
        colors = ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6']

        for i in range(5):
            product_label = tk.Label(products_data_frame, text=f"منتج {i+1}",
                                    font=self.arabic_font, bg=colors[i], fg='white',
                                    relief='solid', bd=1)
            product_label.grid(row=i+1, column=0, sticky='ew', padx=1, pady=1)

            quantity_label = tk.Label(products_data_frame, text="0",
                                     font=self.arabic_font, bg='white',
                                     relief='solid', bd=1)
            quantity_label.grid(row=i+1, column=1, sticky='ew', padx=1, pady=1)

            self.products_data_labels.append((product_label, quantity_label))

        # تكوين الأعمدة
        products_data_frame.grid_columnconfigure(0, weight=2)
        products_data_frame.grid_columnconfigure(1, weight=1)
    
    def create_recent_activities(self):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_frame = ui_enhancements.create_card_frame(
            self.scrollable_frame, title="🕐 الأنشطة الحديثة"
        )
        activities_frame.pack(fill='x', padx=10, pady=10)
        
        # قائمة الأنشطة
        self.activities_listbox = tk.Listbox(activities_frame, font=self.arabic_font,
                                           height=8, bg='white', selectmode='none')
        self.activities_listbox.pack(fill='x', padx=10, pady=10)
        
        # شريط التمرير للقائمة
        activities_scrollbar = ttk.Scrollbar(activities_frame, orient='vertical')
        activities_scrollbar.pack(side='right', fill='y')
        
        self.activities_listbox.config(yscrollcommand=activities_scrollbar.set)
        activities_scrollbar.config(command=self.activities_listbox.yview)
    
    def create_alerts_section(self):
        """إنشاء قسم التنبيهات"""
        alerts_frame = ui_enhancements.create_card_frame(
            self.scrollable_frame, title="⚠️ التنبيهات والإشعارات"
        )
        alerts_frame.pack(fill='x', padx=10, pady=10)
        
        # إطار التنبيهات
        self.alerts_container = tk.Frame(alerts_frame, bg='white')
        self.alerts_container.pack(fill='x', padx=10, pady=10)
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # تحديث بطاقات الملخص
            self.update_summary_cards(cursor)
            
            # تحديث المخططات
            self.update_charts(cursor)
            
            # تحديث الأنشطة الحديثة
            self.update_recent_activities(cursor)
            
            # تحديث التنبيهات
            self.update_alerts(cursor)
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة المعلومات: {str(e)}")
    
    def update_summary_cards(self, cursor):
        """تحديث بطاقات الملخص"""
        today = datetime.now().strftime('%Y-%m-%d')
        current_month = datetime.now().strftime('%Y-%m')
        
        # مبيعات اليوم
        cursor.execute('''
            SELECT COALESCE(SUM(final_amount), 0) 
            FROM sales 
            WHERE DATE(created_at) = ?
        ''', (today,))
        daily_sales = cursor.fetchone()[0]
        self.sales_card.config(text=f"{daily_sales:.2f} جنيه")
        
        # عدد فواتير اليوم
        cursor.execute('''
            SELECT COUNT(*) 
            FROM sales 
            WHERE DATE(created_at) = ?
        ''', (today,))
        daily_invoices = cursor.fetchone()[0]
        self.invoices_card.config(text=f"{daily_invoices} فاتورة")
        
        # المنتجات منخفضة المخزون
        cursor.execute('''
            SELECT COUNT(*) 
            FROM products 
            WHERE quantity <= min_quantity
        ''')
        low_stock_count = cursor.fetchone()[0]
        self.low_stock_card.config(text=f"{low_stock_count} منتج")
        
        # إجمالي العملاء
        cursor.execute('SELECT COUNT(*) FROM customers')
        total_customers = cursor.fetchone()[0]
        self.customers_card.config(text=f"{total_customers} عميل")
        
        # أرباح الشهر (تقديرية)
        cursor.execute('''
            SELECT COALESCE(SUM(final_amount * 0.2), 0) 
            FROM sales 
            WHERE strftime('%Y-%m', created_at) = ?
        ''', (current_month,))
        monthly_profit = cursor.fetchone()[0]
        self.profit_card.config(text=f"{monthly_profit:.2f} جنيه")
        
        # مشتريات الشهر
        cursor.execute('''
            SELECT COALESCE(SUM(final_amount), 0) 
            FROM purchases 
            WHERE strftime('%Y-%m', created_at) = ?
        ''', (current_month,))
        monthly_purchases = cursor.fetchone()[0]
        self.purchases_card.config(text=f"{monthly_purchases:.2f} جنيه")
        
        # ديون العملاء
        cursor.execute('SELECT COALESCE(SUM(balance), 0) FROM customers WHERE balance > 0')
        customer_debts = cursor.fetchone()[0]
        self.debts_card.config(text=f"{customer_debts:.2f} جنيه")
        
        # إجمالي المنتجات
        cursor.execute('SELECT COUNT(*) FROM products')
        total_products = cursor.fetchone()[0]
        self.products_card.config(text=f"{total_products} منتج")
    
    def update_charts(self, cursor):
        """تحديث المخططات البيانية"""
        if MATPLOTLIB_AVAILABLE:
            # تحديث مخطط المبيعات الأسبوعية
            self.update_sales_chart(cursor)

            # تحديث مخطط أفضل المنتجات
            self.update_top_products_chart(cursor)
        else:
            # تحديث المخططات البسيطة
            self.update_simple_charts(cursor)
    
    def update_sales_chart(self, cursor):
        """تحديث مخطط المبيعات الأسبوعية"""
        fig, ax, canvas = self.sales_chart
        
        # جلب بيانات المبيعات لآخر 7 أيام
        dates = []
        sales = []
        
        for i in range(6, -1, -1):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            date_display = (datetime.now() - timedelta(days=i)).strftime('%m-%d')
            
            cursor.execute('''
                SELECT COALESCE(SUM(final_amount), 0) 
                FROM sales 
                WHERE DATE(created_at) = ?
            ''', (date,))
            
            daily_sales = cursor.fetchone()[0]
            dates.append(date_display)
            sales.append(daily_sales)
        
        # تحديث المخطط
        ax.clear()
        ax.plot(dates, sales, marker='o', linewidth=2, markersize=6, color='#27ae60')
        ax.fill_between(dates, sales, alpha=0.3, color='#27ae60')
        
        ax.set_title('مبيعات آخر 7 أيام', fontsize=12, pad=20)
        ax.set_xlabel('التاريخ', fontsize=10)
        ax.set_ylabel('المبيعات (جنيه)', fontsize=10)
        ax.grid(True, alpha=0.3)
        
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        fig.tight_layout()
        canvas.draw()
    
    def update_top_products_chart(self, cursor):
        """تحديث مخطط أفضل المنتجات"""
        fig, ax, canvas = self.products_chart
        
        # جلب أفضل 5 منتجات مبيعاً
        cursor.execute('''
            SELECT p.name, COALESCE(SUM(si.quantity), 0) as total_sold
            FROM products p
            LEFT JOIN sale_items si ON p.id = si.product_id
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT 5
        ''')
        
        results = cursor.fetchall()
        
        if results:
            products = [result[0][:10] + '...' if len(result[0]) > 10 else result[0] for result in results]
            quantities = [result[1] for result in results]
        else:
            products = ['لا توجد بيانات'] * 5
            quantities = [0] * 5
        
        # تحديث المخطط
        ax.clear()
        colors = ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6']
        bars = ax.bar(products, quantities, color=colors)
        
        ax.set_title('أفضل 5 منتجات مبيعاً', fontsize=12, pad=20)
        ax.set_xlabel('المنتجات', fontsize=10)
        ax.set_ylabel('الكمية المباعة', fontsize=10)
        
        # إضافة قيم على الأعمدة
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')
        
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        fig.tight_layout()
        canvas.draw()

    def update_simple_charts(self, cursor):
        """تحديث المخططات البسيطة"""
        # تحديث بيانات المبيعات الأسبوعية
        if hasattr(self, 'sales_data_labels'):
            for i in range(7):
                date = (datetime.now() - timedelta(days=6-i)).strftime('%Y-%m-%d')
                date_display = (datetime.now() - timedelta(days=6-i)).strftime('%m-%d')

                cursor.execute('''
                    SELECT COALESCE(SUM(final_amount), 0)
                    FROM sales
                    WHERE DATE(created_at) = ?
                ''', (date,))

                daily_sales = cursor.fetchone()[0]

                if i < len(self.sales_data_labels):
                    date_label, amount_label = self.sales_data_labels[i]
                    date_label.config(text=date_display)
                    amount_label.config(text=f"{daily_sales:.2f}")

        # تحديث بيانات أفضل المنتجات
        if hasattr(self, 'products_data_labels'):
            cursor.execute('''
                SELECT p.name, COALESCE(SUM(si.quantity), 0) as total_sold
                FROM products p
                LEFT JOIN sale_items si ON p.id = si.product_id
                GROUP BY p.id, p.name
                ORDER BY total_sold DESC
                LIMIT 5
            ''')

            results = cursor.fetchall()

            for i in range(5):
                if i < len(self.products_data_labels):
                    product_label, quantity_label = self.products_data_labels[i]

                    if i < len(results):
                        product_name = results[i][0][:15] + '...' if len(results[i][0]) > 15 else results[i][0]
                        quantity = results[i][1]
                        product_label.config(text=product_name)
                        quantity_label.config(text=str(int(quantity)))
                    else:
                        product_label.config(text="لا توجد بيانات")
                        quantity_label.config(text="0")

    def update_recent_activities(self, cursor):
        """تحديث الأنشطة الحديثة"""
        self.activities_listbox.delete(0, 'end')
        
        # جلب آخر المبيعات
        cursor.execute('''
            SELECT s.created_at, c.name, s.final_amount
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            ORDER BY s.created_at DESC
            LIMIT 10
        ''')
        
        sales = cursor.fetchall()
        
        for sale in sales:
            time_str = sale[0][:16] if sale[0] else ''
            customer_name = sale[1] if sale[1] else 'عميل غير محدد'
            amount = sale[2]
            
            activity = f"🛒 {time_str} - مبيعات لـ {customer_name}: {amount:.2f} جنيه"
            self.activities_listbox.insert('end', activity)
        
        if not sales:
            self.activities_listbox.insert('end', "لا توجد أنشطة حديثة")
    
    def update_alerts(self, cursor):
        """تحديث التنبيهات"""
        # مسح التنبيهات الحالية
        for widget in self.alerts_container.winfo_children():
            widget.destroy()
        
        alert_count = 0
        
        # تنبيهات المخزون المنخفض
        cursor.execute('''
            SELECT name, quantity, min_quantity 
            FROM products 
            WHERE quantity <= min_quantity
            LIMIT 5
        ''')
        
        low_stock_products = cursor.fetchall()
        
        for product in low_stock_products:
            alert_frame = tk.Frame(self.alerts_container, bg='#fff3cd', relief='solid', bd=1)
            alert_frame.pack(fill='x', pady=2)
            
            alert_text = f"⚠️ مخزون منخفض: {product[0]} (الكمية: {product[1]}, الحد الأدنى: {product[2]})"
            alert_label = tk.Label(alert_frame, text=alert_text, font=self.arabic_font,
                                  bg='#fff3cd', fg='#856404')
            alert_label.pack(anchor='w', padx=10, pady=5)
            alert_count += 1
        
        # تنبيهات الديون
        cursor.execute('''
            SELECT name, balance 
            FROM customers 
            WHERE balance > 1000
            LIMIT 3
        ''')
        
        high_debts = cursor.fetchall()
        
        for customer in high_debts:
            alert_frame = tk.Frame(self.alerts_container, bg='#f8d7da', relief='solid', bd=1)
            alert_frame.pack(fill='x', pady=2)
            
            alert_text = f"💳 دين مرتفع: {customer[0]} ({customer[1]:.2f} جنيه)"
            alert_label = tk.Label(alert_frame, text=alert_text, font=self.arabic_font,
                                  bg='#f8d7da', fg='#721c24')
            alert_label.pack(anchor='w', padx=10, pady=5)
            alert_count += 1
        
        if alert_count == 0:
            no_alerts_frame = tk.Frame(self.alerts_container, bg='#d4edda', relief='solid', bd=1)
            no_alerts_frame.pack(fill='x', pady=2)
            
            alert_text = "✅ لا توجد تنبيهات - كل شيء على ما يرام!"
            alert_label = tk.Label(no_alerts_frame, text=alert_text, font=self.arabic_font,
                                  bg='#d4edda', fg='#155724')
            alert_label.pack(anchor='w', padx=10, pady=5)
    
    def refresh_dashboard(self):
        """تحديث لوحة المعلومات"""
        self.load_dashboard_data()
        ui_enhancements.create_notification_toast(
            self.window, "تم تحديث لوحة المعلومات", 'success', 2000
        )
