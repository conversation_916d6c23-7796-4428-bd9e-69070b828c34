#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشخيص مشاكل تسجيل الدخول
"""

import sqlite3
import hashlib
import os
import sys

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def debug_login():
    """تشخيص مشاكل تسجيل الدخول"""
    print("🔍 بدء تشخيص مشاكل تسجيل الدخول...")
    print("="*50)
    
    # 1. فحص وجود قاعدة البيانات
    db_path = "shop_management.db"
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    else:
        print("✅ قاعدة البيانات موجودة")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 2. فحص جدول المستخدمين
        print("\n📋 فحص جدول المستخدمين...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("❌ جدول المستخدمين غير موجود!")
            return False
        else:
            print("✅ جدول المستخدمين موجود")
        
        # 3. فحص هيكل الجدول
        print("\n🏗️ فحص هيكل جدول المستخدمين...")
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print("الأعمدة الموجودة:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 4. فحص المستخدمين الموجودين
        print("\n👥 فحص المستخدمين الموجودين...")
        cursor.execute("SELECT id, username, full_name, role, is_active FROM users")
        users = cursor.fetchall()
        
        if not users:
            print("❌ لا يوجد مستخدمين في قاعدة البيانات!")
            
            # إنشاء مستخدم المدير
            print("👤 إنشاء مستخدم المدير...")
            hashed_password = hash_password("admin123")
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", hashed_password, "المدير العام", "admin", 1))
            conn.commit()
            print("✅ تم إنشاء مستخدم المدير")
        else:
            print("المستخدمين الموجودين:")
            for user in users:
                status = "نشط" if user[4] else "معطل"
                print(f"   - ID: {user[0]}, اسم المستخدم: {user[1]}, الاسم: {user[2]}, الدور: {user[3]}, الحالة: {status}")
        
        # 5. اختبار تسجيل الدخول
        print("\n🧪 اختبار تسجيل الدخول...")
        username = "admin"
        password = "admin123"
        hashed_password = hash_password(password)
        
        print(f"اسم المستخدم: {username}")
        print(f"كلمة المرور: {password}")
        print(f"كلمة المرور المشفرة: {hashed_password[:20]}...")
        
        cursor.execute('''
            SELECT id, username, full_name, role, is_active, password
            FROM users 
            WHERE username = ?
        ''', (username,))
        
        user_data = cursor.fetchone()
        
        if not user_data:
            print("❌ المستخدم غير موجود!")
            return False
        
        print(f"✅ المستخدم موجود: {user_data[1]} ({user_data[2]})")
        print(f"كلمة المرور المحفوظة: {user_data[5][:20]}...")
        
        if user_data[5] == hashed_password:
            print("✅ كلمة المرور صحيحة")
        else:
            print("❌ كلمة المرور غير صحيحة!")
            print("🔧 تحديث كلمة المرور...")
            cursor.execute('UPDATE users SET password = ? WHERE username = ?', 
                          (hashed_password, username))
            conn.commit()
            print("✅ تم تحديث كلمة المرور")
        
        if user_data[4] == 1:
            print("✅ المستخدم نشط")
        else:
            print("❌ المستخدم معطل!")
            cursor.execute('UPDATE users SET is_active = 1 WHERE username = ?', (username,))
            conn.commit()
            print("✅ تم تفعيل المستخدم")
        
        # 6. اختبار نهائي
        print("\n🎯 اختبار نهائي لتسجيل الدخول...")
        cursor.execute('''
            SELECT id, username, full_name, role, is_active
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))
        
        final_test = cursor.fetchone()
        
        if final_test:
            print("✅ اختبار تسجيل الدخول نجح!")
            print(f"   المستخدم: {final_test[1]} ({final_test[2]})")
            print(f"   الدور: {final_test[3]}")
            print(f"   ID: {final_test[0]}")
        else:
            print("❌ اختبار تسجيل الدخول فشل!")
            return False
        
        conn.close()
        
        print("\n" + "="*50)
        print("✅ تم الانتهاء من التشخيص بنجاح!")
        print("📋 بيانات تسجيل الدخول المؤكدة:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_system():
    """اختبار نظام المصادقة"""
    print("\n🔧 اختبار نظام المصادقة...")
    
    try:
        # استيراد نظام المصادقة
        sys.path.append('.')
        from auth_system import auth_system
        
        print("✅ تم استيراد نظام المصادقة")
        
        # اختبار تسجيل الدخول
        success, message = auth_system.login("admin", "admin123")
        
        if success:
            print(f"✅ نظام المصادقة يعمل: {message}")
            
            # فحص المستخدم الحالي
            current_user = auth_system.get_current_user()
            if current_user:
                print(f"✅ المستخدم الحالي: {current_user['full_name']} ({current_user['role']})")
            
            # تسجيل الخروج
            auth_system.logout()
            print("✅ تم تسجيل الخروج")
            
        else:
            print(f"❌ نظام المصادقة لا يعمل: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🔍 تشخيص شامل لمشاكل تسجيل الدخول")
    print("="*60)
    
    # تشخيص قاعدة البيانات
    if debug_login():
        # اختبار نظام المصادقة
        if test_auth_system():
            print("\n🎉 جميع الاختبارات نجحت!")
            print("💡 إذا كانت المشكلة ما زالت موجودة، قد تكون في:")
            print("   1. استيراد المكتبات")
            print("   2. مسار الملفات")
            print("   3. صلاحيات النظام")
        else:
            print("\n⚠️ هناك مشكلة في نظام المصادقة")
    else:
        print("\n❌ فشل في تشخيص قاعدة البيانات")
    
    print("\n🚀 جرب تشغيل البرنامج الآن!")
