#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة المستخدم المتقدمة والمطورة
تحسينات بصرية متقدمة، تأثيرات حديثة، وعناصر تفاعلية
"""

import tkinter as tk
from tkinter import ttk, font
import threading
import time
import math
from datetime import datetime

class AdvancedUI:
    def __init__(self):
        self.setup_modern_styles()
        self.setup_animations()
    
    def setup_modern_styles(self):
        """إعداد الأنماط الحديثة"""
        self.colors = {
            # ألوان أساسية حديثة
            'primary': '#2563eb',      # أزرق حديث
            'secondary': '#64748b',    # رمادي أنيق
            'success': '#10b981',      # أخضر نابض
            'danger': '#ef4444',       # أحمر حيوي
            'warning': '#f59e0b',      # برتقالي دافئ
            'info': '#06b6d4',         # سماوي منعش
            'light': '#f8fafc',        # أبيض ناعم
            'dark': '#1e293b',         # أسود أنيق
            
            # ألوان متدرجة
            'gradient_start': '#667eea',
            'gradient_end': '#764ba2',
            
            # ألوان الخلفية
            'bg_primary': '#ffffff',
            'bg_secondary': '#f1f5f9',
            'bg_card': '#ffffff',
            'bg_hover': '#f8fafc',
            
            # ألوان النص
            'text_primary': '#1e293b',
            'text_secondary': '#64748b',
            'text_muted': '#94a3b8',
            
            # ألوان الحدود
            'border_light': '#e2e8f0',
            'border_medium': '#cbd5e1',
            'border_dark': '#94a3b8',
        }
        
        self.fonts = {
            'heading_xl': ('Segoe UI', 24, 'bold'),
            'heading_lg': ('Segoe UI', 20, 'bold'),
            'heading_md': ('Segoe UI', 16, 'bold'),
            'heading_sm': ('Segoe UI', 14, 'bold'),
            'body_lg': ('Segoe UI', 14),
            'body_md': ('Segoe UI', 12),
            'body_sm': ('Segoe UI', 10),
            'caption': ('Segoe UI', 9),
        }
        
        # أيقونات حديثة
        self.modern_icons = {
            'dashboard': '📊',
            'analytics': '📈',
            'sales': '💰',
            'products': '📦',
            'customers': '👥',
            'suppliers': '🏢',
            'reports': '📋',
            'settings': '⚙️',
            'notifications': '🔔',
            'profile': '👤',
            'logout': '🚪',
            'add': '➕',
            'edit': '✏️',
            'delete': '🗑️',
            'save': '💾',
            'cancel': '❌',
            'search': '🔍',
            'filter': '🔽',
            'refresh': '🔄',
            'export': '📤',
            'import': '📥',
            'print': '🖨️',
            'email': '📧',
            'phone': '📞',
            'location': '📍',
            'calendar': '📅',
            'clock': '🕐',
            'money': '💵',
            'card': '💳',
            'bank': '🏦',
            'chart': '📊',
            'trend_up': '📈',
            'trend_down': '📉',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅',
            'info': 'ℹ️',
            'star': '⭐',
            'heart': '❤️',
            'like': '👍',
            'dislike': '👎',
            'home': '🏠',
            'menu': '☰',
            'close': '✖️',
            'minimize': '➖',
            'maximize': '⬜',
            'back': '⬅️',
            'forward': '➡️',
            'up': '⬆️',
            'down': '⬇️',
            'left': '⬅️',
            'right': '➡️',
        }
    
    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        self.animation_duration = 300  # مللي ثانية
        self.animation_steps = 20
        self.easing_functions = {
            'linear': lambda t: t,
            'ease_in': lambda t: t * t,
            'ease_out': lambda t: 1 - (1 - t) * (1 - t),
            'ease_in_out': lambda t: 2 * t * t if t < 0.5 else 1 - 2 * (1 - t) * (1 - t),
        }
    
    def create_modern_button(self, parent, text, style='primary', size='md', 
                           icon=None, command=None, state='normal', **kwargs):
        """إنشاء زر حديث ومتطور"""
        
        # تحديد الألوان حسب النمط
        style_colors = {
            'primary': {
                'bg': self.colors['primary'],
                'fg': 'white',
                'hover_bg': '#1d4ed8',
                'active_bg': '#1e40af'
            },
            'secondary': {
                'bg': self.colors['secondary'],
                'fg': 'white',
                'hover_bg': '#475569',
                'active_bg': '#334155'
            },
            'success': {
                'bg': self.colors['success'],
                'fg': 'white',
                'hover_bg': '#059669',
                'active_bg': '#047857'
            },
            'danger': {
                'bg': self.colors['danger'],
                'fg': 'white',
                'hover_bg': '#dc2626',
                'active_bg': '#b91c1c'
            },
            'warning': {
                'bg': self.colors['warning'],
                'fg': 'white',
                'hover_bg': '#d97706',
                'active_bg': '#b45309'
            },
            'outline': {
                'bg': 'white',
                'fg': self.colors['primary'],
                'hover_bg': self.colors['bg_hover'],
                'active_bg': self.colors['bg_secondary']
            }
        }
        
        # تحديد الحجم
        size_config = {
            'sm': {'padx': 12, 'pady': 6, 'font': self.fonts['body_sm']},
            'md': {'padx': 16, 'pady': 8, 'font': self.fonts['body_md']},
            'lg': {'padx': 20, 'pady': 12, 'font': self.fonts['body_lg']},
        }
        
        colors = style_colors.get(style, style_colors['primary'])
        size_conf = size_config.get(size, size_config['md'])
        
        # إضافة الأيقونة للنص
        button_text = text
        if icon and icon in self.modern_icons:
            button_text = f"{self.modern_icons[icon]} {text}"
        
        # إنشاء الزر
        button = tk.Button(
            parent,
            text=button_text,
            font=size_conf['font'],
            bg=colors['bg'],
            fg=colors['fg'],
            relief='flat',
            bd=0,
            padx=size_conf['padx'],
            pady=size_conf['pady'],
            cursor='hand2',
            command=command,
            state=state,
            **kwargs
        )
        
        # إضافة تأثيرات التمرير
        def on_enter(e):
            if button['state'] != 'disabled':
                button.config(bg=colors['hover_bg'])
                self.animate_scale(button, 1.0, 1.05, 150)
        
        def on_leave(e):
            if button['state'] != 'disabled':
                button.config(bg=colors['bg'])
                self.animate_scale(button, 1.05, 1.0, 150)
        
        def on_click(e):
            if button['state'] != 'disabled':
                button.config(bg=colors['active_bg'])
                self.animate_press(button)
        
        def on_release(e):
            if button['state'] != 'disabled':
                button.config(bg=colors['hover_bg'])
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
        button.bind('<Button-1>', on_click)
        button.bind('<ButtonRelease-1>', on_release)
        
        return button
    
    def create_modern_card(self, parent, title=None, subtitle=None, **kwargs):
        """إنشاء بطاقة حديثة"""
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief='flat',
            bd=1,
            highlightbackground=self.colors['border_light'],
            highlightthickness=1,
            **kwargs
        )
        
        # إضافة ظل خفيف
        shadow_frame = tk.Frame(
            parent,
            bg=self.colors['border_light'],
            height=2
        )
        
        if title:
            header_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
            header_frame.pack(fill='x', padx=20, pady=(20, 10))
            
            title_label = tk.Label(
                header_frame,
                text=title,
                font=self.fonts['heading_md'],
                fg=self.colors['text_primary'],
                bg=self.colors['bg_card']
            )
            title_label.pack(anchor='w')
            
            if subtitle:
                subtitle_label = tk.Label(
                    header_frame,
                    text=subtitle,
                    font=self.fonts['body_sm'],
                    fg=self.colors['text_secondary'],
                    bg=self.colors['bg_card']
                )
                subtitle_label.pack(anchor='w', pady=(2, 0))
        
        # إضافة تأثير التمرير
        def on_enter(e):
            card_frame.config(highlightbackground=self.colors['border_medium'])
        
        def on_leave(e):
            card_frame.config(highlightbackground=self.colors['border_light'])
        
        card_frame.bind('<Enter>', on_enter)
        card_frame.bind('<Leave>', on_leave)
        
        return card_frame
    
    def create_gradient_frame(self, parent, start_color, end_color, **kwargs):
        """إنشاء إطار بخلفية متدرجة"""
        canvas = tk.Canvas(parent, **kwargs)
        
        def draw_gradient():
            width = canvas.winfo_width()
            height = canvas.winfo_height()
            
            if width > 1 and height > 1:
                canvas.delete("gradient")
                
                # تحويل الألوان إلى RGB
                start_rgb = self.hex_to_rgb(start_color)
                end_rgb = self.hex_to_rgb(end_color)
                
                # رسم التدرج
                for i in range(height):
                    ratio = i / height
                    r = int(start_rgb[0] + (end_rgb[0] - start_rgb[0]) * ratio)
                    g = int(start_rgb[1] + (end_rgb[1] - start_rgb[1]) * ratio)
                    b = int(start_rgb[2] + (end_rgb[2] - start_rgb[2]) * ratio)
                    
                    color = f"#{r:02x}{g:02x}{b:02x}"
                    canvas.create_line(0, i, width, i, fill=color, tags="gradient")
        
        canvas.bind('<Configure>', lambda e: draw_gradient())
        return canvas
    
    def hex_to_rgb(self, hex_color):
        """تحويل اللون من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def animate_scale(self, widget, start_scale, end_scale, duration):
        """تحريك تكبير/تصغير العنصر"""
        def animate():
            steps = 10
            step_duration = duration / steps
            scale_diff = end_scale - start_scale
            
            for i in range(steps + 1):
                current_scale = start_scale + (scale_diff * i / steps)
                # تطبيق التحريك (محاكاة)
                time.sleep(step_duration / 1000)
        
        threading.Thread(target=animate, daemon=True).start()
    
    def animate_press(self, widget):
        """تحريك الضغط على الزر"""
        original_relief = widget.cget('relief')
        widget.config(relief='sunken')
        
        def restore():
            time.sleep(0.1)
            widget.config(relief=original_relief)
        
        threading.Thread(target=restore, daemon=True).start()
    
    def create_loading_spinner(self, parent, size=32, color=None):
        """إنشاء دائرة تحميل متحركة"""
        if not color:
            color = self.colors['primary']
        
        canvas = tk.Canvas(parent, width=size, height=size, bg='white', highlightthickness=0)
        
        def animate_spinner():
            angle = 0
            while True:
                canvas.delete("spinner")
                
                # رسم الدائرة
                center = size // 2
                radius = size // 3
                
                for i in range(8):
                    start_angle = angle + (i * 45)
                    opacity = 1.0 - (i * 0.1)
                    
                    x1 = center + radius * math.cos(math.radians(start_angle))
                    y1 = center + radius * math.sin(math.radians(start_angle))
                    x2 = center + (radius - 5) * math.cos(math.radians(start_angle))
                    y2 = center + (radius - 5) * math.sin(math.radians(start_angle))
                    
                    canvas.create_line(x1, y1, x2, y2, width=3, fill=color, tags="spinner")
                
                angle += 15
                time.sleep(0.1)
        
        threading.Thread(target=animate_spinner, daemon=True).start()
        return canvas
    
    def create_progress_bar(self, parent, value=0, maximum=100, **kwargs):
        """إنشاء شريط تقدم حديث"""
        progress_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], **kwargs)
        
        # الخلفية
        bg_canvas = tk.Canvas(
            progress_frame,
            height=8,
            bg=self.colors['border_light'],
            highlightthickness=0
        )
        bg_canvas.pack(fill='x', padx=2, pady=2)
        
        # الشريط المتقدم
        progress_canvas = tk.Canvas(
            bg_canvas,
            height=8,
            bg=self.colors['primary'],
            highlightthickness=0
        )
        
        def update_progress(new_value):
            percentage = min(new_value / maximum, 1.0)
            width = int(bg_canvas.winfo_width() * percentage)
            progress_canvas.place(x=0, y=0, width=width, height=8)
        
        progress_frame.update_progress = update_progress
        return progress_frame
    
    def create_notification_toast(self, parent, message, type='info', duration=3000):
        """إنشاء إشعار منبثق حديث"""
        toast_colors = {
            'success': {'bg': self.colors['success'], 'fg': 'white', 'icon': '✅'},
            'error': {'bg': self.colors['danger'], 'fg': 'white', 'icon': '❌'},
            'warning': {'bg': self.colors['warning'], 'fg': 'white', 'icon': '⚠️'},
            'info': {'bg': self.colors['info'], 'fg': 'white', 'icon': 'ℹ️'},
        }
        
        colors = toast_colors.get(type, toast_colors['info'])
        
        # إنشاء النافذة المنبثقة
        toast = tk.Toplevel(parent)
        toast.withdraw()
        toast.overrideredirect(True)
        toast.configure(bg=colors['bg'])
        
        # المحتوى
        content_frame = tk.Frame(toast, bg=colors['bg'])
        content_frame.pack(padx=20, pady=15)
        
        icon_label = tk.Label(
            content_frame,
            text=colors['icon'],
            font=self.fonts['heading_sm'],
            bg=colors['bg'],
            fg=colors['fg']
        )
        icon_label.pack(side='left', padx=(0, 10))
        
        message_label = tk.Label(
            content_frame,
            text=message,
            font=self.fonts['body_md'],
            bg=colors['bg'],
            fg=colors['fg']
        )
        message_label.pack(side='left')
        
        # تحديد الموقع
        toast.update_idletasks()
        x = parent.winfo_rootx() + parent.winfo_width() - toast.winfo_width() - 20
        y = parent.winfo_rooty() + 20
        toast.geometry(f"+{x}+{y}")
        
        # إظهار الإشعار
        toast.deiconify()
        
        # إخفاء الإشعار بعد المدة المحددة
        def hide_toast():
            time.sleep(duration / 1000)
            try:
                toast.destroy()
            except:
                pass
        
        threading.Thread(target=hide_toast, daemon=True).start()
        
        return toast

# إنشاء مثيل عام
advanced_ui = AdvancedUI()
