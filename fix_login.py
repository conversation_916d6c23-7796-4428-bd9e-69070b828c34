#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إصلاح مشكلة تسجيل الدخول
"""

import sqlite3
import hashlib
import os

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def fix_login_issue():
    """إصلاح مشكلة تسجيل الدخول"""
    try:
        # الاتصال بقاعدة البيانات
        db_path = "shop_management.db"
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة!")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 فحص جدول المستخدمين...")
        
        # التحقق من وجود جدول المستخدمين
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='users'
        """)
        
        if not cursor.fetchone():
            print("📝 إنشاء جدول المستخدمين...")
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'cashier', 'inventory')),
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    created_by INTEGER,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            print("✅ تم إنشاء جدول المستخدمين")
        
        # التحقق من وجود مستخدم admin
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = "admin"')
        admin_exists = cursor.fetchone()[0]
        
        if admin_exists == 0:
            print("👤 إنشاء مستخدم المدير الافتراضي...")
            hashed_password = hash_password("admin123")
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", hashed_password, "المدير العام", "admin", 1))
            print("✅ تم إنشاء مستخدم المدير")
        else:
            print("👤 تحديث كلمة مرور المدير...")
            hashed_password = hash_password("admin123")
            cursor.execute('''
                UPDATE users SET password = ?, is_active = 1 
                WHERE username = "admin"
            ''', (hashed_password,))
            print("✅ تم تحديث كلمة مرور المدير")
        
        # إنشاء جدول الجلسات إذا لم يكن موجوداً
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='user_sessions'
        """)
        
        if not cursor.fetchone():
            print("📝 إنشاء جدول الجلسات...")
            cursor.execute('''
                CREATE TABLE user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    logout_time TIMESTAMP,
                    ip_address TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            print("✅ تم إنشاء جدول الجلسات")
        
        conn.commit()
        conn.close()
        
        print("\n" + "="*50)
        print("✅ تم إصلاح مشكلة تسجيل الدخول بنجاح!")
        print("📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح تسجيل الدخول: {str(e)}")
        return False

def test_login():
    """اختبار تسجيل الدخول"""
    try:
        conn = sqlite3.connect("shop_management.db")
        cursor = conn.cursor()
        
        username = "admin"
        password = "admin123"
        hashed_password = hash_password(password)
        
        cursor.execute('''
            SELECT id, username, full_name, role, is_active
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            print("✅ اختبار تسجيل الدخول نجح!")
            print(f"   المستخدم: {user[1]} ({user[2]})")
            print(f"   الدور: {user[3]}")
            return True
        else:
            print("❌ اختبار تسجيل الدخول فشل!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 بدء إصلاح مشكلة تسجيل الدخول...")
    
    if fix_login_issue():
        print("\n🧪 اختبار تسجيل الدخول...")
        test_login()
    
    print("\n🎯 يمكنك الآن تشغيل البرنامج مرة أخرى!")
