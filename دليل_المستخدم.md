# دليل المستخدم - نظام إدارة المحل

## التثبيت والتشغيل

### 1. التثبيت الأولي
1. تأكد من تثبيت Python 3.7 أو أحدث على جهازك
2. قم بتشغيل ملف `install_requirements.bat` لتثبيت المكتبات المطلوبة
3. قم بتشغيل ملف `setup_sample_data.bat` لإضافة بيانات تجريبية (اختياري)
4. قم بتشغيل ملف `run.bat` أو `python main.py` لبدء البرنامج

### 2. الشاشة الرئيسية
عند تشغيل البرنامج ستظهر الشاشة الرئيسية التي تحتوي على:
- أزرار الوصول السريع للوظائف الرئيسية
- إحصائيات سريعة (عدد المنتجات، المنتجات منخفضة المخزون، مبيعات اليوم)
- شريط القوائم العلوي

## الوظائف الرئيسية

### 1. إدارة المنتجات

#### إضافة منتج جديد:
1. اضغط على "إدارة المنتجات" من الشاشة الرئيسية
2. املأ بيانات المنتج:
   - **اسم المنتج**: اسم المنتج (مطلوب)
   - **الباركود**: رقم الباركود (اختياري)
   - **التصنيف**: اختر التصنيف المناسب
   - **سعر الشراء**: السعر الذي تشتري به المنتج
   - **سعر البيع**: السعر الذي تبيع به المنتج
   - **الكمية**: الكمية الحالية في المخزون
   - **الحد الأدنى**: الحد الأدنى للتنبيه (افتراضي: 5)
   - **الوحدة**: وحدة القياس (قطعة، كيلو، لتر، إلخ)
   - **الوصف**: وصف إضافي للمنتج
3. اضغط "إضافة"

#### تعديل منتج:
1. ابحث عن المنتج في القائمة أو استخدم خانة البحث
2. انقر على المنتج لتحديده
3. ستظهر بياناته في النموذج
4. عدّل البيانات المطلوبة
5. اضغط "تحديث"

#### حذف منتج:
1. حدد المنتج من القائمة
2. اضغط "حذف"
3. أكد الحذف

### 2. نقطة البيع (POS)

#### إنشاء فاتورة مبيعات:
1. اضغط على "نقطة البيع" من الشاشة الرئيسية
2. اختر العميل (أو اتركه "عميل نقدي")
3. ابحث عن المنتجات المطلوبة:
   - استخدم خانة البحث للبحث بالاسم أو الباركود
   - انقر نقراً مزدوجاً على المنتج لإضافته
4. أدخل الكمية المطلوبة
5. كرر العملية لجميع المنتجات
6. أدخل الخصم والضريبة إن وجدت
7. اضغط "إتمام البيع"

#### تعديل عناصر الفاتورة:
- **حذف منتج**: حدد المنتج في العربة واضغط Delete أو انقر بالزر الأيمن واختر "حذف"
- **تعديل الكمية**: انقر بالزر الأيمن على المنتج واختر "تعديل الكمية"

### 3. المشتريات

#### إنشاء فاتورة شراء:
1. اضغط على "فاتورة شراء" من الشاشة الرئيسية
2. اختر المورد
3. ابحث عن المنتجات وأضفها للفاتورة
4. أدخل الكمية وسعر الوحدة لكل منتج
5. أدخل الخصم والضريبة إن وجدت
6. اضغط "إتمام الشراء"

**ملاحظة**: سيتم تحديث كميات المنتجات في المخزون تلقائياً

### 4. إدارة العملاء

#### إضافة عميل جديد:
1. اضغط على "إدارة العملاء"
2. املأ البيانات:
   - **اسم العميل**: (مطلوب)
   - **رقم الهاتف**: (اختياري)
   - **العنوان**: (اختياري)
   - **البريد الإلكتروني**: (اختياري)
   - **الملاحظات**: (اختياري)
3. اضغط "إضافة"

### 5. إدارة الموردين

#### إضافة مورد جديد:
1. اضغط على "إدارة الموردين"
2. املأ البيانات المطلوبة
3. اضغط "إضافة"

### 6. إدارة التصنيفات

#### إضافة تصنيف جديد:
1. من قائمة "المنتجات" اختر "إدارة التصنيفات"
2. أدخل اسم التصنيف والوصف
3. اضغط "إضافة"

## النسخ الاحتياطي

### إنشاء نسخة احتياطية:
1. من قائمة "ملف" اختر "نسخة احتياطية"
2. سيتم إنشاء ملف نسخة احتياطية بالتاريخ والوقت الحالي

### استعادة نسخة احتياطية:
1. من قائمة "ملف" اختر "استعادة"
2. اختر ملف النسخة الاحتياطية
3. أكد الاستعادة

## نصائح مهمة

### للحصول على أفضل أداء:
- قم بعمل نسخة احتياطية بانتظام
- احذف البيانات القديمة غير المطلوبة
- تأكد من دقة أسعار المنتجات قبل البيع

### لتجنب الأخطاء:
- تأكد من إدخال الكميات بشكل صحيح
- راجع الفواتير قبل إتمامها
- تأكد من اختيار العميل/المورد الصحيح

### للبحث السريع:
- استخدم أجزاء من اسم المنتج للبحث
- يمكن البحث بالباركود إذا كان متوفراً
- استخدم النقر المزدوج لإضافة المنتجات بسرعة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "خطأ في فتح قاعدة البيانات":
- تأكد من وجود صلاحيات الكتابة في مجلد البرنامج
- أغلق أي نسخة أخرى من البرنامج

#### "المنتج غير متوفر":
- تحقق من كمية المنتج في المخزون
- قم بتحديث المخزون من خلال فاتورة شراء

#### "خطأ في حساب الإجمالي":
- تأكد من إدخال أرقام صحيحة في خانات الأسعار
- تحقق من نسبة الضريبة والخصم

#### مشاكل في عرض النص العربي:
- تأكد من تثبيت خطوط عربية على النظام
- أعد تشغيل البرنامج

## الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md للمعلومات التقنية
3. تأكد من تحديث البرنامج لآخر إصدار

---

**ملاحظة**: هذا البرنامج مصمم للاستخدام المحلي ولا يتطلب اتصال بالإنترنت.
