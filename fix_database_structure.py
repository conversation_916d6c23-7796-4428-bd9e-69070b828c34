#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إصلاح هيكل قاعدة البيانات
"""

import sqlite3
import hashlib
import os

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def fix_database_structure():
    """إصلاح هيكل قاعدة البيانات"""
    print("🔧 بدء إصلاح هيكل قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect("shop_management.db")
        cursor = conn.cursor()
        
        # 1. فحص الأعمدة الموجودة
        print("📋 فحص هيكل جدول المستخدمين الحالي...")
        cursor.execute("PRAGMA table_info(users)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        print(f"الأعمدة الموجودة: {existing_columns}")
        
        # 2. إضافة الأعمدة المفقودة
        required_columns = {
            'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'last_login': 'TIMESTAMP',
            'created_by': 'INTEGER'
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                print(f"➕ إضافة العمود: {column}")
                try:
                    cursor.execute(f"ALTER TABLE users ADD COLUMN {column} {column_type}")
                    print(f"✅ تم إضافة العمود: {column}")
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة العمود {column}: {str(e)}")
        
        # 3. إنشاء جدول الجلسات إذا لم يكن موجود
        print("📋 فحص جدول الجلسات...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_sessions'")
        if not cursor.fetchone():
            print("➕ إنشاء جدول الجلسات...")
            cursor.execute('''
                CREATE TABLE user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    logout_time TIMESTAMP,
                    ip_address TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            print("✅ تم إنشاء جدول الجلسات")
        else:
            print("✅ جدول الجلسات موجود")
        
        # 4. التأكد من وجود مستخدم المدير
        print("👤 فحص مستخدم المدير...")
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            print("➕ إنشاء مستخدم المدير...")
            hashed_password = hash_password("admin123")
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", hashed_password, "المدير العام", "admin", 1))
            print("✅ تم إنشاء مستخدم المدير")
        else:
            print("✅ مستخدم المدير موجود")
            # تحديث كلمة المرور للتأكد
            hashed_password = hash_password("admin123")
            cursor.execute('''
                UPDATE users SET password = ?, is_active = 1 
                WHERE username = 'admin'
            ''', (hashed_password,))
            print("✅ تم تحديث بيانات المدير")
        
        conn.commit()
        
        # 5. اختبار نهائي
        print("🧪 اختبار نهائي...")
        cursor.execute("PRAGMA table_info(users)")
        final_columns = [col[1] for col in cursor.fetchall()]
        print(f"الأعمدة النهائية: {final_columns}")
        
        # اختبار تسجيل الدخول
        hashed_password = hash_password("admin123")
        cursor.execute('''
            SELECT id, username, full_name, role, is_active, last_login
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', ("admin", hashed_password))
        
        user = cursor.fetchone()
        if user:
            print("✅ اختبار تسجيل الدخول نجح!")
            print(f"   المستخدم: {user[1]} ({user[2]})")
            print(f"   الدور: {user[3]}")
        else:
            print("❌ اختبار تسجيل الدخول فشل!")
            return False
        
        conn.close()
        
        print("\n" + "="*50)
        print("✅ تم إصلاح هيكل قاعدة البيانات بنجاح!")
        print("📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_system_after_fix():
    """اختبار نظام المصادقة بعد الإصلاح"""
    print("\n🧪 اختبار نظام المصادقة بعد الإصلاح...")
    
    try:
        import sys
        sys.path.append('.')
        
        # إعادة تحميل نظام المصادقة
        if 'auth_system' in sys.modules:
            del sys.modules['auth_system']
        
        from auth_system import auth_system
        
        print("✅ تم استيراد نظام المصادقة")
        
        # اختبار تسجيل الدخول
        success, message = auth_system.login("admin", "admin123")
        
        if success:
            print(f"✅ نظام المصادقة يعمل: {message}")
            
            # فحص المستخدم الحالي
            current_user = auth_system.get_current_user()
            if current_user:
                print(f"✅ المستخدم الحالي: {current_user['full_name']} ({current_user['role']})")
            
            # تسجيل الخروج
            auth_system.logout()
            print("✅ تم تسجيل الخروج")
            
            return True
        else:
            print(f"❌ نظام المصادقة لا يعمل: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 إصلاح شامل لهيكل قاعدة البيانات")
    print("="*60)
    
    if fix_database_structure():
        if test_auth_system_after_fix():
            print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
            print("🚀 يمكنك الآن تشغيل البرنامج بدون مشاكل!")
        else:
            print("\n⚠️ هناك مشكلة في نظام المصادقة")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")
