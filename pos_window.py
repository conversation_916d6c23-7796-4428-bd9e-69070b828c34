import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Product, Customer, Sale
from datetime import datetime
from ui_enhancements import ui_enhancements

class POSWindow:
    def __init__(self, parent):
        self.parent = parent
        self.product_model = Product()
        self.customer_model = Customer()
        self.sale_model = Sale()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("نقطة البيع")
        self.window.geometry("1200x800")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # متغيرات الفاتورة
        self.cart_items = []
        self.total_amount = 0.0
        self.discount = 0.0
        self.tax_rate = 0.0
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_customers()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة نقطة البيع"""
        # إطار العنوان المحسن
        title_frame = tk.Frame(self.window, bg='#27ae60', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        # عنوان مع أيقونة ومعلومات
        title_content = tk.Frame(title_frame, bg='#27ae60')
        title_content.pack(expand=True, fill='both')

        title_label = tk.Label(title_content, text="🛒 نقطة البيع",
                              font=self.arabic_font_large, fg='white', bg='#27ae60')
        title_label.pack(side='left', padx=20, pady=20)

        # معلومات الوقت والتاريخ
        time_label = tk.Label(title_content, text=datetime.now().strftime("📅 %Y-%m-%d | 🕐 %H:%M"),
                             font=self.arabic_font, fg='#d5f4e6', bg='#27ae60')
        time_label.pack(side='right', padx=20, pady=20)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار البحث والمنتجات (يسار)
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        self.create_search_section(left_frame)
        self.create_products_section(left_frame)
        
        # إطار الفاتورة (يمين)
        right_frame = tk.Frame(main_frame, bg='#f0f0f0', width=400)
        right_frame.pack(side='right', fill='y', padx=5)
        right_frame.pack_propagate(False)
        
        self.create_invoice_section(right_frame)
    
    def create_search_section(self, parent):
        """إنشاء قسم البحث"""
        search_frame = tk.LabelFrame(parent, text="البحث عن المنتجات", 
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        search_frame.pack(fill='x', pady=5)
        
        # إطار البحث الداخلي
        inner_frame = tk.Frame(search_frame, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(inner_frame, text="البحث:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        
        self.search_entry = tk.Entry(inner_frame, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_products)
        self.search_entry.bind('<Return>', self.search_products)
        
        search_btn = ui_enhancements.create_styled_button(
            inner_frame, "بحث", style='primary', icon='search',
            font=self.arabic_font, command=self.search_products
        )
        search_btn.pack(side='left', padx=5)

        # زر مسح البحث
        clear_btn = ui_enhancements.create_styled_button(
            inner_frame, "مسح", style='secondary', icon='refresh',
            font=self.arabic_font, command=self.clear_search
        )
        clear_btn.pack(side='left', padx=5)
    
    def create_products_section(self, parent):
        """إنشاء قسم عرض المنتجات"""
        products_frame = tk.LabelFrame(parent, text="المنتجات المتاحة", 
                                      font=self.arabic_font_bold, bg='#f0f0f0')
        products_frame.pack(fill='both', expand=True, pady=5)
        
        # جدول المنتجات
        columns = ('ID', 'الاسم', 'الباركود', 'السعر', 'الكمية المتاحة', 'الوحدة')
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
        
        self.products_tree.column('ID', width=50)
        self.products_tree.column('الاسم', width=200)
        self.products_tree.column('الباركود', width=100)
        self.products_tree.column('السعر', width=80)
        self.products_tree.column('الكمية المتاحة', width=100)
        self.products_tree.column('الوحدة', width=60)
        
        # شريط التمرير
        scrollbar_products = ttk.Scrollbar(products_frame, orient='vertical', 
                                          command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar_products.set)
        
        # تخطيط الجدول
        self.products_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_products.pack(side='right', fill='y')
        
        # ربط النقر المزدوج لإضافة المنتج
        self.products_tree.bind('<Double-1>', self.add_product_to_cart)
        
        # تحميل جميع المنتجات في البداية
        self.load_all_products()
    
    def create_invoice_section(self, parent):
        """إنشاء قسم الفاتورة"""
        # معلومات العميل
        customer_frame = tk.LabelFrame(parent, text="معلومات العميل", 
                                      font=self.arabic_font_bold, bg='#f0f0f0')
        customer_frame.pack(fill='x', pady=5)
        
        tk.Label(customer_frame, text="العميل:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        
        self.customer_combo = ttk.Combobox(customer_frame, font=self.arabic_font, 
                                          width=25, state='readonly')
        self.customer_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # عربة التسوق
        cart_frame = tk.LabelFrame(parent, text="عربة التسوق", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        cart_frame.pack(fill='both', expand=True, pady=5)
        
        # جدول عربة التسوق
        cart_columns = ('المنتج', 'الكمية', 'السعر', 'الإجمالي')
        self.cart_tree = ttk.Treeview(cart_frame, columns=cart_columns, show='headings', height=10)
        
        for col in cart_columns:
            self.cart_tree.heading(col, text=col)
        
        self.cart_tree.column('المنتج', width=150)
        self.cart_tree.column('الكمية', width=60)
        self.cart_tree.column('السعر', width=70)
        self.cart_tree.column('الإجمالي', width=80)
        
        # شريط التمرير للعربة
        scrollbar_cart = ttk.Scrollbar(cart_frame, orient='vertical', 
                                      command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=scrollbar_cart.set)
        
        self.cart_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_cart.pack(side='right', fill='y')
        
        # ربط النقر لحذف عنصر من العربة
        self.cart_tree.bind('<Delete>', self.remove_from_cart)
        self.cart_tree.bind('<Button-3>', self.show_cart_context_menu)
        
        # إطار الحسابات
        calc_frame = tk.LabelFrame(parent, text="الحسابات", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        calc_frame.pack(fill='x', pady=5)
        
        # الإجمالي الفرعي
        tk.Label(calc_frame, text="الإجمالي الفرعي:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=2)
        self.subtotal_label = tk.Label(calc_frame, text="0.00", font=self.arabic_font_bold, 
                                      bg='#f0f0f0', fg='blue')
        self.subtotal_label.grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        # الخصم
        tk.Label(calc_frame, text="الخصم:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='e', padx=5, pady=2)
        self.discount_entry = tk.Entry(calc_frame, font=self.arabic_font, width=10)
        self.discount_entry.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        self.discount_entry.bind('<KeyRelease>', self.calculate_total)
        self.discount_entry.insert(0, "0")
        
        # الضريبة
        tk.Label(calc_frame, text="الضريبة %:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=2, column=0, sticky='e', padx=5, pady=2)
        self.tax_entry = tk.Entry(calc_frame, font=self.arabic_font, width=10)
        self.tax_entry.grid(row=2, column=1, sticky='w', padx=5, pady=2)
        self.tax_entry.bind('<KeyRelease>', self.calculate_total)
        self.tax_entry.insert(0, "0")
        
        # الإجمالي النهائي
        tk.Label(calc_frame, text="الإجمالي النهائي:", font=self.arabic_font_bold, bg='#f0f0f0').grid(
            row=3, column=0, sticky='e', padx=5, pady=5)
        self.total_label = tk.Label(calc_frame, text="0.00", font=self.arabic_font_large, 
                                   bg='#f0f0f0', fg='red')
        self.total_label.grid(row=3, column=1, sticky='w', padx=5, pady=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.pack(fill='x', pady=10)
        
        # زر إتمام البيع
        complete_btn = ui_enhancements.create_styled_button(
            buttons_frame, "إتمام البيع", style='success', icon='money',
            font=self.arabic_font_bold, height=2, command=self.complete_sale
        )
        complete_btn.pack(fill='x', pady=2)

        # زر مسح العربة
        clear_btn = ui_enhancements.create_styled_button(
            buttons_frame, "مسح العربة", style='danger', icon='delete',
            font=self.arabic_font, command=self.clear_cart
        )
        clear_btn.pack(fill='x', pady=2)

        # زر طباعة الفاتورة
        print_btn = ui_enhancements.create_styled_button(
            buttons_frame, "طباعة الفاتورة", style='primary', icon='print',
            font=self.arabic_font, command=self.print_invoice
        )
        print_btn.pack(fill='x', pady=2)
    
    def load_customers(self):
        """تحميل العملاء"""
        customers = self.customer_model.get_all_customers()
        customer_names = ['عميل نقدي'] + [customer[1] for customer in customers]
        self.customer_combo['values'] = customer_names
        self.customer_combo.set('عميل نقدي')
        
        # حفظ معرفات العملاء
        self.customers_dict = {'عميل نقدي': None}
        for customer in customers:
            self.customers_dict[customer[1]] = customer[0]
    
    def load_all_products(self):
        """تحميل جميع المنتجات"""
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تحميل المنتجات
        products = self.product_model.get_all_products()
        for product in products:
            # عرض المنتجات المتوفرة فقط
            if product[6] > 0:  # quantity > 0
                self.products_tree.insert('', 'end', values=(
                    product[0],  # ID
                    product[1],  # name
                    product[2] or '',  # barcode
                    f"{product[5]:.2f}",  # selling_price
                    product[6],  # quantity
                    product[8]   # unit
                ))
    
    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_entry.get().strip()
        
        # مسح الجدول
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        if search_term:
            products = self.product_model.search_products(search_term)
        else:
            products = self.product_model.get_all_products()
        
        # عرض النتائج (المنتجات المتوفرة فقط)
        for product in products:
            if product[6] > 0:  # quantity > 0
                self.products_tree.insert('', 'end', values=(
                    product[0],  # ID
                    product[1],  # name
                    product[2] or '',  # barcode
                    f"{product[5]:.2f}",  # selling_price
                    product[6],  # quantity
                    product[8]   # unit
                ))
    
    def clear_search(self):
        """مسح البحث"""
        self.search_entry.delete(0, 'end')
        self.load_all_products()
    
    def add_product_to_cart(self, event=None):
        """إضافة منتج إلى العربة"""
        selection = self.products_tree.selection()
        if not selection:
            return
        
        item = self.products_tree.item(selection[0])
        product_data = item['values']
        
        product_id = product_data[0]
        product_name = product_data[1]
        product_price = float(product_data[3])
        available_quantity = int(product_data[4])
        
        # طلب الكمية من المستخدم
        quantity_window = tk.Toplevel(self.window)
        quantity_window.title("إدخال الكمية")
        quantity_window.geometry("300x150")
        quantity_window.configure(bg='#f0f0f0')
        quantity_window.transient(self.window)
        quantity_window.grab_set()
        
        tk.Label(quantity_window, text=f"المنتج: {product_name}", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=10)
        tk.Label(quantity_window, text=f"الكمية المتاحة: {available_quantity}", 
                font=self.arabic_font, bg='#f0f0f0').pack()
        
        tk.Label(quantity_window, text="الكمية المطلوبة:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        
        quantity_entry = tk.Entry(quantity_window, font=self.arabic_font, width=10)
        quantity_entry.pack(pady=5)
        quantity_entry.insert(0, "1")
        quantity_entry.focus()
        quantity_entry.select_range(0, 'end')
        
        def add_to_cart():
            try:
                quantity = int(quantity_entry.get())
                if quantity <= 0:
                    messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                    return
                
                if quantity > available_quantity:
                    messagebox.showerror("خطأ", f"الكمية المطلوبة أكبر من المتاح ({available_quantity})")
                    return
                
                # التحقق من وجود المنتج في العربة
                existing_item = None
                for cart_item in self.cart_items:
                    if cart_item['product_id'] == product_id:
                        existing_item = cart_item
                        break
                
                if existing_item:
                    # زيادة الكمية
                    new_quantity = existing_item['quantity'] + quantity
                    if new_quantity > available_quantity:
                        messagebox.showerror("خطأ", f"إجمالي الكمية سيتجاوز المتاح ({available_quantity})")
                        return
                    existing_item['quantity'] = new_quantity
                    existing_item['total_price'] = new_quantity * product_price
                else:
                    # إضافة منتج جديد
                    cart_item = {
                        'product_id': product_id,
                        'product_name': product_name,
                        'quantity': quantity,
                        'unit_price': product_price,
                        'total_price': quantity * product_price
                    }
                    self.cart_items.append(cart_item)
                
                self.update_cart_display()
                self.calculate_total()
                quantity_window.destroy()
                
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال رقم صحيح للكمية")
        
        btn_frame = tk.Frame(quantity_window, bg='#f0f0f0')
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="إضافة", font=self.arabic_font,
                 bg='#27ae60', fg='white', command=add_to_cart).pack(side='left', padx=5)
        tk.Button(btn_frame, text="إلغاء", font=self.arabic_font,
                 bg='#e74c3c', fg='white', command=quantity_window.destroy).pack(side='left', padx=5)
        
        # ربط Enter بإضافة المنتج
        quantity_entry.bind('<Return>', lambda e: add_to_cart())
    
    def update_cart_display(self):
        """تحديث عرض العربة"""
        # مسح العربة
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        
        # إضافة العناصر
        for cart_item in self.cart_items:
            self.cart_tree.insert('', 'end', values=(
                cart_item['product_name'],
                cart_item['quantity'],
                f"{cart_item['unit_price']:.2f}",
                f"{cart_item['total_price']:.2f}"
            ))
    
    def calculate_total(self, event=None):
        """حساب الإجمالي"""
        # حساب الإجمالي الفرعي
        subtotal = sum(item['total_price'] for item in self.cart_items)
        self.subtotal_label.config(text=f"{subtotal:.2f}")
        
        # حساب الخصم
        try:
            discount = float(self.discount_entry.get() or 0)
        except ValueError:
            discount = 0
        
        # حساب الضريبة
        try:
            tax_rate = float(self.tax_entry.get() or 0)
        except ValueError:
            tax_rate = 0
        
        # حساب الإجمالي النهائي
        after_discount = subtotal - discount
        tax_amount = after_discount * (tax_rate / 100)
        final_total = after_discount + tax_amount
        
        self.total_label.config(text=f"{final_total:.2f}")
        
        # حفظ القيم
        self.total_amount = subtotal
        self.discount = discount
        self.tax_rate = tax_rate
    
    def remove_from_cart(self, event=None):
        """حذف عنصر من العربة"""
        selection = self.cart_tree.selection()
        if selection:
            item_index = self.cart_tree.index(selection[0])
            del self.cart_items[item_index]
            self.update_cart_display()
            self.calculate_total()
    
    def show_cart_context_menu(self, event):
        """عرض قائمة السياق للعربة"""
        selection = self.cart_tree.selection()
        if selection:
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="حذف", command=self.remove_from_cart)
            context_menu.add_command(label="تعديل الكمية", command=self.edit_quantity)
            context_menu.tk_popup(event.x_root, event.y_root)
    
    def edit_quantity(self):
        """تعديل كمية منتج في العربة"""
        selection = self.cart_tree.selection()
        if not selection:
            return
        
        item_index = self.cart_tree.index(selection[0])
        cart_item = self.cart_items[item_index]
        
        # نافذة تعديل الكمية
        edit_window = tk.Toplevel(self.window)
        edit_window.title("تعديل الكمية")
        edit_window.geometry("300x150")
        edit_window.configure(bg='#f0f0f0')
        edit_window.transient(self.window)
        edit_window.grab_set()
        
        tk.Label(edit_window, text=f"المنتج: {cart_item['product_name']}", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=10)
        tk.Label(edit_window, text="الكمية الجديدة:", 
                font=self.arabic_font, bg='#f0f0f0').pack(pady=5)
        
        quantity_entry = tk.Entry(edit_window, font=self.arabic_font, width=10)
        quantity_entry.pack(pady=5)
        quantity_entry.insert(0, str(cart_item['quantity']))
        quantity_entry.focus()
        quantity_entry.select_range(0, 'end')
        
        def update_quantity():
            try:
                new_quantity = int(quantity_entry.get())
                if new_quantity <= 0:
                    # حذف المنتج إذا كانت الكمية صفر أو أقل
                    del self.cart_items[item_index]
                else:
                    cart_item['quantity'] = new_quantity
                    cart_item['total_price'] = new_quantity * cart_item['unit_price']
                
                self.update_cart_display()
                self.calculate_total()
                edit_window.destroy()
                
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال رقم صحيح للكمية")
        
        btn_frame = tk.Frame(edit_window, bg='#f0f0f0')
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', command=update_quantity).pack(side='left', padx=5)
        tk.Button(btn_frame, text="إلغاء", font=self.arabic_font,
                 bg='#e74c3c', fg='white', command=edit_window.destroy).pack(side='left', padx=5)
        
        quantity_entry.bind('<Return>', lambda e: update_quantity())
    
    def clear_cart(self):
        """مسح العربة"""
        if self.cart_items:
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح العربة؟"):
                self.cart_items.clear()
                self.update_cart_display()
                self.calculate_total()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            messagebox.showerror("خطأ", "العربة فارغة")
            return
        
        # الحصول على معرف العميل
        customer_name = self.customer_combo.get()
        customer_id = self.customers_dict.get(customer_name)
        
        # تحضير بيانات العناصر
        items = []
        for cart_item in self.cart_items:
            items.append({
                'product_id': cart_item['product_id'],
                'quantity': cart_item['quantity'],
                'unit_price': cart_item['unit_price']
            })
        
        # إنشاء الفاتورة
        success, sale_id, invoice_number, message = self.sale_model.create_sale(
            customer_id, items, self.discount, self.tax_rate, 'نقدي', ''
        )
        
        if success:
            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة بنجاح\nرقم الفاتورة: {invoice_number}")
            
            # حفظ معرف الفاتورة للطباعة
            self.last_sale_id = sale_id
            
            # مسح العربة
            self.cart_items.clear()
            self.update_cart_display()
            self.calculate_total()
            
            # تحديث قائمة المنتجات
            self.load_all_products()
            
            # عرض خيار الطباعة
            if messagebox.askyesno("طباعة", "هل تريد طباعة الفاتورة؟"):
                self.print_invoice()
        else:
            messagebox.showerror("خطأ", message)
    
    def print_invoice(self):
        """طباعة الفاتورة"""
        if hasattr(self, 'last_sale_id'):
            try:
                from pdf_generator import pdf_generator
                from tkinter import filedialog

                # اختيار مكان الحفظ
                filename = filedialog.asksaveasfilename(
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    title="حفظ فاتورة المبيعات"
                )

                if filename:
                    # إنشاء ملف PDF
                    saved_file = pdf_generator.generate_sale_invoice_pdf(self.last_sale_id, filename)
                    messagebox.showinfo("نجح", f"تم حفظ الفاتورة في:\n{saved_file}")

                    # فتح الملف
                    import os
                    os.startfile(saved_file)

            except ImportError:
                messagebox.showerror("خطأ", "مكتبة ReportLab غير مثبتة\nقم بتثبيتها باستخدام: pip install reportlab")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء الفاتورة: {str(e)}")
        else:
            messagebox.showerror("خطأ", "لا توجد فاتورة للطباعة")
