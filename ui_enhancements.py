#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تحسينات واجهة المستخدم
يحتوي على أدوات وتحسينات لواجهة المستخدم
"""

import tkinter as tk
from tkinter import ttk, font
import os

class UIEnhancements:
    def __init__(self):
        self.setup_styles()
        self.setup_icons()
    
    def setup_styles(self):
        """إعداد أنماط واجهة المستخدم"""
        self.colors = {
            'primary': '#3498db',
            'secondary': '#2c3e50',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40',
            'background': '#f0f0f0',
            'surface': '#ffffff',
            'text_primary': '#2c3e50',
            'text_secondary': '#7f8c8d'
        }
        
        self.button_styles = {
            'primary': {'bg': self.colors['primary'], 'fg': 'white', 'relief': 'flat'},
            'success': {'bg': self.colors['success'], 'fg': 'white', 'relief': 'flat'},
            'warning': {'bg': self.colors['warning'], 'fg': 'white', 'relief': 'flat'},
            'danger': {'bg': self.colors['danger'], 'fg': 'white', 'relief': 'flat'},
            'secondary': {'bg': self.colors['secondary'], 'fg': 'white', 'relief': 'flat'},
            'light': {'bg': self.colors['light'], 'fg': self.colors['text_primary'], 'relief': 'raised'}
        }
    
    def setup_icons(self):
        """إعداد الأيقونات (نصية)"""
        self.icons = {
            'add': '➕',
            'edit': '✏️',
            'delete': '🗑️',
            'save': '💾',
            'print': '🖨️',
            'search': '🔍',
            'refresh': '🔄',
            'settings': '⚙️',
            'user': '👤',
            'users': '👥',
            'product': '📦',
            'cart': '🛒',
            'money': '💰',
            'report': '📊',
            'notification': '🔔',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅',
            'info': 'ℹ️',
            'home': '🏠',
            'back': '⬅️',
            'forward': '➡️',
            'up': '⬆️',
            'down': '⬇️',
            'close': '✖️',
            'minimize': '➖',
            'maximize': '⬜',
            'menu': '☰',
            'export': '📤',
            'import': '📥',
            'calendar': '📅',
            'clock': '🕐',
            'phone': '📞',
            'email': '📧',
            'location': '📍'
        }
    
    def create_styled_button(self, parent, text, style='primary', icon=None, command=None, **kwargs):
        """إنشاء زر بتصميم محسن"""
        button_config = self.button_styles.get(style, self.button_styles['primary']).copy()
        button_config.update(kwargs)
        
        # إضافة الأيقونة إلى النص
        if icon and icon in self.icons:
            text = f"{self.icons[icon]} {text}"
        
        button = tk.Button(parent, text=text, command=command, **button_config)
        
        # إضافة تأثيرات التفاعل
        self.add_hover_effect(button, style)
        
        return button
    
    def add_hover_effect(self, widget, style='primary'):
        """إضافة تأثير التمرير للعنصر"""
        original_bg = widget.cget('bg')
        
        # ألوان التمرير
        hover_colors = {
            'primary': '#2980b9',
            'success': '#229954',
            'warning': '#d68910',
            'danger': '#cb4335',
            'secondary': '#1b2631',
            'light': '#e8e9ea'
        }
        
        hover_bg = hover_colors.get(style, hover_colors['primary'])
        
        def on_enter(event):
            widget.config(bg=hover_bg)
        
        def on_leave(event):
            widget.config(bg=original_bg)
        
        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)
    
    def create_card_frame(self, parent, title=None, **kwargs):
        """إنشاء إطار بتصميم بطاقة"""
        card_config = {
            'bg': self.colors['surface'],
            'relief': 'raised',
            'bd': 1,
            'padx': 10,
            'pady': 10
        }
        card_config.update(kwargs)
        
        card = tk.Frame(parent, **card_config)
        
        if title:
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', pady=(0, 10))
            
            title_label = tk.Label(title_frame, text=title, 
                                  font=('Arial Unicode MS', 12, 'bold'),
                                  bg=self.colors['surface'], 
                                  fg=self.colors['text_primary'])
            title_label.pack(side='left')
            
            # خط فاصل
            separator = tk.Frame(title_frame, height=1, bg=self.colors['text_secondary'])
            separator.pack(fill='x', pady=(5, 0))
        
        return card
    
    def create_status_badge(self, parent, text, status='info', **kwargs):
        """إنشاء شارة حالة"""
        badge_colors = {
            'success': {'bg': '#d4edda', 'fg': '#155724'},
            'warning': {'bg': '#fff3cd', 'fg': '#856404'},
            'danger': {'bg': '#f8d7da', 'fg': '#721c24'},
            'info': {'bg': '#d1ecf1', 'fg': '#0c5460'},
            'primary': {'bg': '#cce5ff', 'fg': '#004085'}
        }
        
        badge_config = badge_colors.get(status, badge_colors['info'])
        badge_config.update({
            'relief': 'flat',
            'padx': 8,
            'pady': 4,
            'font': ('Arial Unicode MS', 9)
        })
        badge_config.update(kwargs)
        
        badge = tk.Label(parent, text=text, **badge_config)
        return badge
    
    def create_progress_bar(self, parent, value=0, maximum=100, **kwargs):
        """إنشاء شريط تقدم محسن"""
        progress_config = {
            'length': 200,
            'mode': 'determinate'
        }
        progress_config.update(kwargs)
        
        progress = ttk.Progressbar(parent, **progress_config)
        progress['maximum'] = maximum
        progress['value'] = value
        
        return progress
    
    def create_search_box(self, parent, placeholder="البحث...", command=None, **kwargs):
        """إنشاء صندوق بحث محسن"""
        search_frame = tk.Frame(parent, bg=self.colors['background'])
        
        # أيقونة البحث
        search_icon = tk.Label(search_frame, text=self.icons['search'], 
                              bg=self.colors['background'], 
                              fg=self.colors['text_secondary'])
        search_icon.pack(side='left', padx=(5, 0))
        
        # حقل البحث
        search_config = {
            'font': ('Arial Unicode MS', 10),
            'relief': 'flat',
            'bd': 5,
            'bg': self.colors['surface']
        }
        search_config.update(kwargs)
        
        search_entry = tk.Entry(search_frame, **search_config)
        search_entry.pack(side='left', fill='x', expand=True, padx=5)
        
        # إضافة placeholder
        self.add_placeholder(search_entry, placeholder)
        
        # ربط الأحداث
        if command:
            search_entry.bind('<KeyRelease>', lambda e: command(search_entry.get()))
        
        return search_frame, search_entry
    
    def add_placeholder(self, entry, placeholder_text):
        """إضافة نص placeholder للحقل"""
        def on_focus_in(event):
            if entry.get() == placeholder_text:
                entry.delete(0, 'end')
                entry.config(fg=self.colors['text_primary'])
        
        def on_focus_out(event):
            if not entry.get():
                entry.insert(0, placeholder_text)
                entry.config(fg=self.colors['text_secondary'])
        
        # إعداد أولي
        entry.insert(0, placeholder_text)
        entry.config(fg=self.colors['text_secondary'])
        
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
    
    def create_notification_toast(self, parent, message, type='info', duration=3000):
        """إنشاء إشعار منبثق"""
        toast_colors = {
            'success': {'bg': '#d4edda', 'fg': '#155724'},
            'warning': {'bg': '#fff3cd', 'fg': '#856404'},
            'error': {'bg': '#f8d7da', 'fg': '#721c24'},
            'info': {'bg': '#d1ecf1', 'fg': '#0c5460'}
        }
        
        color_config = toast_colors.get(type, toast_colors['info'])
        
        # إنشاء نافذة الإشعار
        toast = tk.Toplevel(parent)
        toast.wm_overrideredirect(True)
        toast.configure(bg=color_config['bg'])
        
        # موضع الإشعار
        x = parent.winfo_rootx() + parent.winfo_width() - 300
        y = parent.winfo_rooty() + 50
        toast.geometry(f"280x60+{x}+{y}")
        
        # محتوى الإشعار
        frame = tk.Frame(toast, bg=color_config['bg'], padx=15, pady=10)
        frame.pack(fill='both', expand=True)
        
        # أيقونة
        icon_map = {'success': 'success', 'warning': 'warning', 'error': 'error', 'info': 'info'}
        icon = tk.Label(frame, text=self.icons[icon_map[type]], 
                       bg=color_config['bg'], fg=color_config['fg'],
                       font=('Arial Unicode MS', 14))
        icon.pack(side='left', padx=(0, 10))
        
        # النص
        text_label = tk.Label(frame, text=message, 
                             bg=color_config['bg'], fg=color_config['fg'],
                             font=('Arial Unicode MS', 10), wraplength=200)
        text_label.pack(side='left', fill='x', expand=True)
        
        # زر الإغلاق
        close_btn = tk.Label(frame, text=self.icons['close'], 
                            bg=color_config['bg'], fg=color_config['fg'],
                            font=('Arial Unicode MS', 10), cursor='hand2')
        close_btn.pack(side='right')
        close_btn.bind('<Button-1>', lambda e: toast.destroy())
        
        # إغلاق تلقائي
        toast.after(duration, toast.destroy)
        
        return toast
    
    def setup_keyboard_shortcuts(self, window):
        """إعداد اختصارات لوحة المفاتيح"""
        shortcuts = {
            '<Control-n>': 'new',
            '<Control-o>': 'open',
            '<Control-s>': 'save',
            '<Control-p>': 'print',
            '<Control-f>': 'find',
            '<Control-r>': 'refresh',
            '<F1>': 'help',
            '<F5>': 'refresh',
            '<Escape>': 'cancel'
        }
        
        for key, action in shortcuts.items():
            window.bind(key, lambda e, a=action: self.handle_shortcut(a))
    
    def handle_shortcut(self, action):
        """التعامل مع اختصارات لوحة المفاتيح"""
        # يمكن تخصيص هذه الدالة حسب الحاجة
        print(f"تم تنفيذ الاختصار: {action}")
    
    def create_modern_treeview(self, parent, columns, **kwargs):
        """إنشاء جدول بتصميم حديث"""
        # إنشاء إطار للجدول
        tree_frame = tk.Frame(parent, bg=self.colors['background'])
        
        # إنشاء الجدول
        tree_config = {
            'show': 'headings',
            'selectmode': 'extended'
        }
        tree_config.update(kwargs)
        
        tree = ttk.Treeview(tree_frame, columns=columns, **tree_config)
        
        # تكوين الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=tree.xview)
        tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط العناصر
        tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        return tree_frame, tree
    
    def apply_theme(self, window, theme='light'):
        """تطبيق سمة على النافذة"""
        if theme == 'dark':
            bg_color = '#2c3e50'
            fg_color = '#ecf0f1'
        else:
            bg_color = '#f0f0f0'
            fg_color = '#2c3e50'
        
        window.configure(bg=bg_color)
        
        # تطبيق السمة على جميع العناصر الفرعية
        for widget in window.winfo_children():
            self._apply_theme_recursive(widget, bg_color, fg_color)
    
    def _apply_theme_recursive(self, widget, bg_color, fg_color):
        """تطبيق السمة بشكل تكراري"""
        try:
            if isinstance(widget, (tk.Frame, tk.Label, tk.Button)):
                widget.configure(bg=bg_color, fg=fg_color)
            
            for child in widget.winfo_children():
                self._apply_theme_recursive(child, bg_color, fg_color)
        except:
            pass

# إنشاء مثيل من تحسينات واجهة المستخدم
ui_enhancements = UIEnhancements()
