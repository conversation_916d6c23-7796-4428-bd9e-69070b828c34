#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف إعداد البيانات التجريبية لنظام إدارة المحل
يقوم بإضافة بيانات تجريبية للاختبار
"""

from models import Product, Category, Customer, Supplier

def setup_sample_data():
    """إضافة بيانات تجريبية للنظام"""
    
    print("إضافة بيانات تجريبية...")
    
    # إنشاء نماذج البيانات
    product_model = Product()
    category_model = Category()
    customer_model = Customer()
    supplier_model = Supplier()
    
    # إضافة عملاء تجريبيين
    customers_data = [
        ("أحمد محمد", "01234567890", "القاهرة، مصر", "<EMAIL>", "عميل مميز"),
        ("فاطمة علي", "01987654321", "الإسكندرية، مصر", "<EMAIL>", "عميل جديد"),
        ("محمد حسن", "01122334455", "الجيزة، مصر", "<EMAIL>", "عميل منتظم"),
    ]
    
    print("إضافة العملاء...")
    for name, phone, address, email, notes in customers_data:
        success, customer_id, message = customer_model.add_customer(name, phone, address, email, notes)
        if success:
            print(f"✓ تم إضافة العميل: {name}")
        else:
            print(f"✗ خطأ في إضافة العميل {name}: {message}")
    
    # إضافة موردين تجريبيين
    suppliers_data = [
        ("شركة الأغذية المتحدة", "02123456789", "القاهرة الجديدة", "<EMAIL>", "مورد المواد الغذائية"),
        ("مؤسسة الأدوات المنزلية", "02987654321", "مدينة نصر", "<EMAIL>", "مورد الأدوات"),
        ("شركة مستحضرات التجميل", "02555666777", "المعادي", "<EMAIL>", "مورد مستحضرات التجميل"),
    ]
    
    print("إضافة الموردين...")
    for name, phone, address, email, notes in suppliers_data:
        success, supplier_id, message = supplier_model.add_supplier(name, phone, address, email, notes)
        if success:
            print(f"✓ تم إضافة المورد: {name}")
        else:
            print(f"✗ خطأ في إضافة المورد {name}: {message}")
    
    # الحصول على التصنيفات الموجودة
    categories = category_model.get_all_categories()
    categories_dict = {cat[1]: cat[0] for cat in categories}
    
    # إضافة منتجات تجريبية
    products_data = [
        # مواد غذائية
        ("أرز أبيض 1 كيلو", "1234567890123", "مواد غذائية", 15.0, 18.0, 50, 10, "كيس", "أرز مصري عالي الجودة"),
        ("سكر أبيض 1 كيلو", "1234567890124", "مواد غذائية", 12.0, 15.0, 30, 5, "كيس", "سكر أبيض نقي"),
        ("زيت طبخ 1 لتر", "1234567890125", "مواد غذائية", 25.0, 30.0, 20, 5, "زجاجة", "زيت دوار الشمس"),
        ("شاي أحمر", "1234567890126", "مواد غذائية", 8.0, 12.0, 40, 10, "علبة", "شاي أحمر فاخر"),
        ("قهوة سريعة التحضير", "1234567890127", "مواد غذائية", 20.0, 25.0, 25, 5, "برطمان", "قهوة سريعة التحضير"),
        
        # أدوات منزلية
        ("مقلاة تيفال", "2234567890123", "أدوات منزلية", 80.0, 120.0, 15, 3, "قطعة", "مقلاة غير لاصقة 28 سم"),
        ("طقم أكواب زجاج", "2234567890124", "أدوات منزلية", 45.0, 65.0, 10, 2, "طقم", "طقم 6 أكواب زجاج"),
        ("مفرش طاولة", "2234567890125", "أدوات منزلية", 30.0, 45.0, 8, 2, "قطعة", "مفرش طاولة قطني"),
        ("سكاكين مطبخ", "2234567890126", "أدوات منزلية", 60.0, 85.0, 12, 3, "طقم", "طقم سكاكين ستانلس ستيل"),
        
        # مستحضرات تجميل
        ("شامبو للشعر", "3234567890123", "مستحضرات تجميل", 25.0, 35.0, 20, 5, "زجاجة", "شامبو للشعر الجاف"),
        ("كريم مرطب", "3234567890124", "مستحضرات تجميل", 40.0, 55.0, 15, 3, "علبة", "كريم مرطب للبشرة"),
        ("معجون أسنان", "3234567890125", "مستحضرات تجميل", 12.0, 18.0, 30, 8, "أنبوب", "معجون أسنان بالفلورايد"),
        
        # قرطاسية
        ("دفتر ملاحظات", "4234567890123", "قرطاسية", 8.0, 12.0, 25, 5, "قطعة", "دفتر ملاحظات 100 ورقة"),
        ("أقلام حبر جاف", "4234567890124", "قرطاسية", 15.0, 22.0, 40, 10, "علبة", "علبة 12 قلم حبر جاف"),
        ("مسطرة 30 سم", "4234567890125", "قرطاسية", 3.0, 5.0, 50, 15, "قطعة", "مسطرة بلاستيك شفافة"),
    ]
    
    print("إضافة المنتجات...")
    for name, barcode, category_name, purchase_price, selling_price, quantity, min_quantity, unit, description in products_data:
        category_id = categories_dict.get(category_name)
        if category_id:
            success, product_id, message = product_model.add_product(
                name, barcode, category_id, purchase_price, selling_price, 
                quantity, min_quantity, unit, description
            )
            if success:
                print(f"✓ تم إضافة المنتج: {name}")
            else:
                print(f"✗ خطأ في إضافة المنتج {name}: {message}")
        else:
            print(f"✗ تصنيف غير موجود: {category_name}")
    
    print("\n" + "="*50)
    print("تم إضافة البيانات التجريبية بنجاح!")
    print("يمكنك الآن تشغيل البرنامج واستكشاف الميزات")
    print("="*50)

if __name__ == "__main__":
    setup_sample_data()
