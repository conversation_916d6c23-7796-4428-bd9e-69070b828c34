import tkinter as tk
from tkinter import ttk, messagebox, font
from models import Customer
from ui_enhancements import ui_enhancements

class CustomersWindow:
    def __init__(self, parent):
        self.parent = parent
        self.customer_model = Customer()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة العملاء")
        self.window.geometry("900x600")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_customers()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=10, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة إدارة العملاء"""
        # إطار العنوان المحسن
        title_frame = tk.Frame(self.window, bg='#8e44ad', height=70)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        # عنوان مع أيقونة
        title_content = tk.Frame(title_frame, bg='#8e44ad')
        title_content.pack(expand=True, fill='both')

        title_label = tk.Label(title_content, text="👥 إدارة العملاء",
                              font=self.arabic_font_bold, fg='white', bg='#8e44ad')
        title_label.pack(side='left', padx=20, pady=15)

        # معلومات سريعة
        info_label = tk.Label(title_content, text="إضافة وتعديل بيانات العملاء",
                             font=self.arabic_font, fg='#d5b7e8', bg='#8e44ad')
        info_label.pack(side='right', padx=20, pady=15)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار النموذج (يسار)
        form_frame = tk.LabelFrame(main_frame, text="بيانات العميل", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        form_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        self.create_customer_form(form_frame)
        
        # إطار القائمة (يمين)
        list_frame = tk.LabelFrame(main_frame, text="قائمة العملاء", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        self.create_customers_list(list_frame)
    
    def create_customer_form(self, parent):
        """إنشاء نموذج إدخال العميل"""
        # اسم العميل
        tk.Label(parent, text="اسم العميل:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=0, column=0, sticky='e', padx=5, pady=5)
        self.name_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # رقم الهاتف
        tk.Label(parent, text="رقم الهاتف:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=1, column=0, sticky='e', padx=5, pady=5)
        self.phone_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.phone_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # العنوان
        tk.Label(parent, text="العنوان:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=2, column=0, sticky='ne', padx=5, pady=5)
        self.address_text = tk.Text(parent, font=self.arabic_font, width=25, height=3)
        self.address_text.grid(row=2, column=1, padx=5, pady=5)
        
        # البريد الإلكتروني
        tk.Label(parent, text="البريد الإلكتروني:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=3, column=0, sticky='e', padx=5, pady=5)
        self.email_entry = tk.Entry(parent, font=self.arabic_font, width=25)
        self.email_entry.grid(row=3, column=1, padx=5, pady=5)
        
        # الملاحظات
        tk.Label(parent, text="الملاحظات:", font=self.arabic_font, bg='#f0f0f0').grid(
            row=4, column=0, sticky='ne', padx=5, pady=5)
        self.notes_text = tk.Text(parent, font=self.arabic_font, width=25, height=4)
        self.notes_text.grid(row=4, column=1, padx=5, pady=5)
        
        # الأزرار
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        self.add_btn = ui_enhancements.create_styled_button(
            buttons_frame, "إضافة", style='success', icon='add',
            font=self.arabic_font, width=10, command=self.add_customer
        )
        self.add_btn.pack(side='left', padx=5)

        self.update_btn = ui_enhancements.create_styled_button(
            buttons_frame, "تحديث", style='primary', icon='edit',
            font=self.arabic_font, width=10, command=self.update_customer, state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)

        self.delete_btn = ui_enhancements.create_styled_button(
            buttons_frame, "حذف", style='danger', icon='delete',
            font=self.arabic_font, width=10, command=self.delete_customer, state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)

        self.clear_btn = ui_enhancements.create_styled_button(
            buttons_frame, "مسح", style='secondary', icon='refresh',
            font=self.arabic_font, width=10, command=self.clear_form
        )
        self.clear_btn.pack(side='left', padx=5)

        self.account_btn = ui_enhancements.create_styled_button(
            buttons_frame, "كشف حساب", style='warning', icon='report',
            font=self.arabic_font, width=10, command=self.show_account_statement, state='disabled'
        )
        self.account_btn.pack(side='left', padx=5)
    
    def create_customers_list(self, parent):
        """إنشاء قائمة العملاء"""
        # إطار البحث
        search_frame = tk.Frame(parent, bg='#f0f0f0')
        search_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(search_frame, text="البحث:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.search_entry = tk.Entry(search_frame, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_customers)
        
        search_btn = tk.Button(search_frame, text="بحث", font=self.arabic_font,
                              bg='#34495e', fg='white', command=self.search_customers)
        search_btn.pack(side='left', padx=5)
        
        # جدول العملاء
        columns = ('ID', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'الرصيد')
        self.customers_tree = ttk.Treeview(parent, columns=columns, show='headings', height=20)
        
        # تكوين الأعمدة
        self.customers_tree.heading('ID', text='ID')
        self.customers_tree.heading('الاسم', text='الاسم')
        self.customers_tree.heading('الهاتف', text='الهاتف')
        self.customers_tree.heading('البريد الإلكتروني', text='البريد الإلكتروني')
        self.customers_tree.heading('الرصيد', text='الرصيد')
        
        # تحديد عرض الأعمدة
        self.customers_tree.column('ID', width=50)
        self.customers_tree.column('الاسم', width=150)
        self.customers_tree.column('الهاتف', width=120)
        self.customers_tree.column('البريد الإلكتروني', width=180)
        self.customers_tree.column('الرصيد', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.customers_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # ربط حدث النقر على العميل
        self.customers_tree.bind('<ButtonRelease-1>', self.on_customer_select)
        
        # متغير لحفظ ID العميل المحدد
        self.selected_customer_id = None
    
    def load_customers(self):
        """تحميل العملاء"""
        # مسح الجدول
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        # تحميل العملاء
        customers = self.customer_model.get_all_customers()
        for customer in customers:
            self.customers_tree.insert('', 'end', values=(
                customer[0],  # ID
                customer[1],  # name
                customer[2] or '',  # phone
                customer[4] or '',  # email
                f"{customer[5]:.2f}"  # balance
            ))
    
    def search_customers(self, event=None):
        """البحث في العملاء"""
        search_term = self.search_entry.get()
        
        # مسح الجدول
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        # البحث في العملاء
        customers = self.customer_model.get_all_customers()
        for customer in customers:
            if (search_term.lower() in customer[1].lower() or 
                (customer[2] and search_term in customer[2]) or
                (customer[4] and search_term.lower() in customer[4].lower())):
                
                self.customers_tree.insert('', 'end', values=(
                    customer[0],  # ID
                    customer[1],  # name
                    customer[2] or '',  # phone
                    customer[4] or '',  # email
                    f"{customer[5]:.2f}"  # balance
                ))
    
    def on_customer_select(self, event):
        """عند تحديد عميل من القائمة"""
        selection = self.customers_tree.selection()
        if selection:
            item = self.customers_tree.item(selection[0])
            customer_id = item['values'][0]
            
            # جلب بيانات العميل
            customers = self.customer_model.get_all_customers()
            customer = None
            for c in customers:
                if c[0] == customer_id:
                    customer = c
                    break
            
            if customer:
                self.fill_form_with_customer(customer)
                self.selected_customer_id = customer_id
                
                # تفعيل أزرار التحديث والحذف وكشف الحساب
                self.update_btn.config(state='normal')
                self.delete_btn.config(state='normal')
                self.account_btn.config(state='normal')
    
    def fill_form_with_customer(self, customer):
        """ملء النموذج ببيانات العميل"""
        self.clear_form()
        
        self.name_entry.insert(0, customer[1])
        if customer[2]:
            self.phone_entry.insert(0, customer[2])
        if customer[3]:
            self.address_text.insert('1.0', customer[3])
        if customer[4]:
            self.email_entry.insert(0, customer[4])
        if customer[6]:
            self.notes_text.insert('1.0', customer[6])
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_entry.delete(0, 'end')
        self.phone_entry.delete(0, 'end')
        self.address_text.delete('1.0', 'end')
        self.email_entry.delete(0, 'end')
        self.notes_text.delete('1.0', 'end')
        
        self.selected_customer_id = None
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        self.account_btn.config(state='disabled')
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم العميل")
            return False
        return True
    
    def add_customer(self):
        """إضافة عميل جديد"""
        if not self.validate_form():
            return
        
        # جمع البيانات
        name = self.name_entry.get().strip()
        phone = self.phone_entry.get().strip()
        address = self.address_text.get('1.0', 'end').strip()
        email = self.email_entry.get().strip()
        notes = self.notes_text.get('1.0', 'end').strip()
        
        # إضافة العميل
        success, customer_id, message = self.customer_model.add_customer(
            name, phone, address, email, notes
        )
        
        if success:
            messagebox.showinfo("نجح", message)
            self.clear_form()
            self.load_customers()
        else:
            messagebox.showerror("خطأ", message)
    
    def update_customer(self):
        """تحديث العميل"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يجب تحديد عميل للتحديث")
            return
        
        if not self.validate_form():
            return
        
        messagebox.showinfo("قريباً", "ميزة تحديث العميل قيد التطوير")
    
    def delete_customer(self):
        """حذف العميل"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يجب تحديد عميل للحذف")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا العميل؟"):
            messagebox.showinfo("قريباً", "ميزة حذف العميل قيد التطوير")

    def show_account_statement(self):
        """عرض كشف حساب العميل"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يجب تحديد عميل لعرض كشف حسابه")
            return

        from accounts_window import AccountsWindow
        AccountsWindow(self.window, 'customer', self.selected_customer_id)
