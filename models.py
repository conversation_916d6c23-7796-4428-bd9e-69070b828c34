from database import db
from datetime import datetime
import sqlite3

class Product:
    def __init__(self):
        self.db = db
    
    def add_product(self, name, barcode, category_id, purchase_price, selling_price, 
                   quantity=0, min_quantity=5, unit='قطعة', description=''):
        """إضافة منتج جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO products (name, barcode, category_id, purchase_price, 
                                    selling_price, quantity, min_quantity, unit, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, barcode, category_id, purchase_price, selling_price, 
                  quantity, min_quantity, unit, description))
            
            product_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return True, product_id, "تم إضافة المنتج بنجاح"
        except sqlite3.IntegrityError:
            return False, None, "الباركود موجود مسبقاً"
        except Exception as e:
            return False, None, f"خطأ في إضافة المنتج: {str(e)}"
    
    def update_product(self, product_id, name, barcode, category_id, purchase_price, 
                      selling_price, quantity, min_quantity, unit, description):
        """تحديث بيانات منتج"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE products SET name=?, barcode=?, category_id=?, purchase_price=?,
                                  selling_price=?, quantity=?, min_quantity=?, unit=?, 
                                  description=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (name, barcode, category_id, purchase_price, selling_price, 
                  quantity, min_quantity, unit, description, product_id))
            
            conn.commit()
            conn.close()
            return True, "تم تحديث المنتج بنجاح"
        except sqlite3.IntegrityError:
            return False, "الباركود موجود مسبقاً"
        except Exception as e:
            return False, f"خطأ في تحديث المنتج: {str(e)}"
    
    def delete_product(self, product_id):
        """حذف منتج"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM products WHERE id=?', (product_id,))
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "تم حذف المنتج بنجاح"
            else:
                conn.close()
                return False, "المنتج غير موجود"
        except Exception as e:
            return False, f"خطأ في حذف المنتج: {str(e)}"
    
    def get_product(self, product_id):
        """الحصول على بيانات منتج"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.id=?
            ''', (product_id,))
            
            product = cursor.fetchone()
            conn.close()
            return product
        except Exception as e:
            print(f"خطأ في جلب بيانات المنتج: {str(e)}")
            return None
    
    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                ORDER BY p.name
            ''')
            
            products = cursor.fetchall()
            conn.close()
            return products
        except Exception as e:
            print(f"خطأ في جلب المنتجات: {str(e)}")
            return []
    
    def search_products(self, search_term):
        """البحث في المنتجات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.name LIKE ? OR p.barcode LIKE ?
                ORDER BY p.name
            ''', (f'%{search_term}%', f'%{search_term}%'))
            
            products = cursor.fetchall()
            conn.close()
            return products
        except Exception as e:
            print(f"خطأ في البحث: {str(e)}")
            return []
    
    def get_low_stock_products(self):
        """الحصول على المنتجات منخفضة المخزون"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.quantity <= p.min_quantity
                ORDER BY p.quantity
            ''')
            
            products = cursor.fetchall()
            conn.close()
            return products
        except Exception as e:
            print(f"خطأ في جلب المنتجات منخفضة المخزون: {str(e)}")
            return []
    
    def update_quantity(self, product_id, new_quantity):
        """تحديث كمية المنتج"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE products SET quantity=?, updated_at=CURRENT_TIMESTAMP 
                WHERE id=?
            ''', (new_quantity, product_id))
            
            conn.commit()
            conn.close()
            return True, "تم تحديث الكمية بنجاح"
        except Exception as e:
            return False, f"خطأ في تحديث الكمية: {str(e)}"

class Category:
    def __init__(self):
        self.db = db
    
    def add_category(self, name, description=''):
        """إضافة تصنيف جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO categories (name, description) VALUES (?, ?)
            ''', (name, description))
            
            category_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return True, category_id, "تم إضافة التصنيف بنجاح"
        except sqlite3.IntegrityError:
            return False, None, "اسم التصنيف موجود مسبقاً"
        except Exception as e:
            return False, None, f"خطأ في إضافة التصنيف: {str(e)}"
    
    def get_all_categories(self):
        """الحصول على جميع التصنيفات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM categories ORDER BY name')
            categories = cursor.fetchall()
            conn.close()
            return categories
        except Exception as e:
            print(f"خطأ في جلب التصنيفات: {str(e)}")
            return []

class Customer:
    def __init__(self):
        self.db = db
    
    def add_customer(self, name, phone='', address='', email='', notes=''):
        """إضافة عميل جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO customers (name, phone, address, email, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, address, email, notes))
            
            customer_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return True, customer_id, "تم إضافة العميل بنجاح"
        except Exception as e:
            return False, None, f"خطأ في إضافة العميل: {str(e)}"
    
    def get_all_customers(self):
        """الحصول على جميع العملاء"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM customers ORDER BY name')
            customers = cursor.fetchall()
            conn.close()
            return customers
        except Exception as e:
            print(f"خطأ في جلب العملاء: {str(e)}")
            return []

class Supplier:
    def __init__(self):
        self.db = db

    def add_supplier(self, name, phone='', address='', email='', notes=''):
        """إضافة مورد جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO suppliers (name, phone, address, email, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, address, email, notes))

            supplier_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return True, supplier_id, "تم إضافة المورد بنجاح"
        except Exception as e:
            return False, None, f"خطأ في إضافة المورد: {str(e)}"

    def get_all_suppliers(self):
        """الحصول على جميع الموردين"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM suppliers ORDER BY name')
            suppliers = cursor.fetchall()
            conn.close()
            return suppliers
        except Exception as e:
            print(f"خطأ في جلب الموردين: {str(e)}")
            return []

class Sale:
    def __init__(self):
        self.db = db

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM sales')
            count = cursor.fetchone()[0]
            conn.close()

            return f"INV-{datetime.now().strftime('%Y%m%d')}-{count + 1:04d}"
        except Exception as e:
            return f"INV-{datetime.now().strftime('%Y%m%d')}-0001"

    def create_sale(self, customer_id, items, discount=0, tax_rate=0, payment_method='نقدي', notes=''):
        """إنشاء فاتورة مبيعات جديدة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # حساب الإجمالي
            total_amount = sum(item['quantity'] * item['unit_price'] for item in items)
            tax_amount = total_amount * (tax_rate / 100)
            final_amount = total_amount - discount + tax_amount

            # إنشاء الفاتورة
            invoice_number = self.generate_invoice_number()
            cursor.execute('''
                INSERT INTO sales (invoice_number, customer_id, total_amount, discount,
                                 tax_rate, tax_amount, final_amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (invoice_number, customer_id, total_amount, discount, tax_rate,
                  tax_amount, final_amount, payment_method, notes))

            sale_id = cursor.lastrowid

            # إضافة تفاصيل الفاتورة وتحديث المخزون
            for item in items:
                cursor.execute('''
                    INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ''', (sale_id, item['product_id'], item['quantity'],
                      item['unit_price'], item['quantity'] * item['unit_price']))

                # تحديث كمية المنتج في المخزون
                cursor.execute('''
                    UPDATE products SET quantity = quantity - ? WHERE id = ?
                ''', (item['quantity'], item['product_id']))

            conn.commit()
            conn.close()
            return True, sale_id, invoice_number, "تم إنشاء فاتورة المبيعات بنجاح"
        except Exception as e:
            return False, None, None, f"خطأ في إنشاء فاتورة المبيعات: {str(e)}"

    def get_sale(self, sale_id):
        """الحصول على بيانات فاتورة مبيعات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # بيانات الفاتورة الأساسية
            cursor.execute('''
                SELECT s.*, c.name as customer_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                WHERE s.id = ?
            ''', (sale_id,))
            sale = cursor.fetchone()

            # تفاصيل الفاتورة
            cursor.execute('''
                SELECT si.*, p.name as product_name, p.unit
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                WHERE si.sale_id = ?
            ''', (sale_id,))
            items = cursor.fetchall()

            conn.close()
            return sale, items
        except Exception as e:
            print(f"خطأ في جلب بيانات الفاتورة: {str(e)}")
            return None, []

class Purchase:
    def __init__(self):
        self.db = db

    def generate_invoice_number(self):
        """توليد رقم فاتورة شراء جديد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM purchases')
            count = cursor.fetchone()[0]
            conn.close()

            return f"PUR-{datetime.now().strftime('%Y%m%d')}-{count + 1:04d}"
        except Exception as e:
            return f"PUR-{datetime.now().strftime('%Y%m%d')}-0001"

    def create_purchase(self, supplier_id, items, discount=0, tax_rate=0, payment_method='نقدي', notes=''):
        """إنشاء فاتورة مشتريات جديدة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # حساب الإجمالي
            total_amount = sum(item['quantity'] * item['unit_price'] for item in items)
            tax_amount = total_amount * (tax_rate / 100)
            final_amount = total_amount - discount + tax_amount

            # إنشاء الفاتورة
            invoice_number = self.generate_invoice_number()
            cursor.execute('''
                INSERT INTO purchases (invoice_number, supplier_id, total_amount, discount,
                                     tax_rate, tax_amount, final_amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (invoice_number, supplier_id, total_amount, discount, tax_rate,
                  tax_amount, final_amount, payment_method, notes))

            purchase_id = cursor.lastrowid

            # إضافة تفاصيل الفاتورة وتحديث المخزون
            for item in items:
                cursor.execute('''
                    INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ''', (purchase_id, item['product_id'], item['quantity'],
                      item['unit_price'], item['quantity'] * item['unit_price']))

                # تحديث كمية المنتج في المخزون
                cursor.execute('''
                    UPDATE products SET quantity = quantity + ? WHERE id = ?
                ''', (item['quantity'], item['product_id']))

            conn.commit()
            conn.close()
            return True, purchase_id, invoice_number, "تم إنشاء فاتورة المشتريات بنجاح"
        except Exception as e:
            return False, None, None, f"خطأ في إنشاء فاتورة المشتريات: {str(e)}"
