#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
الواجهة الرئيسية المطورة والحديثة
تصميم متقدم مع تأثيرات بصرية وتفاعل محسن
"""

import tkinter as tk
from tkinter import ttk, font
from datetime import datetime
from advanced_ui import advanced_ui
from database import db

class ModernMainInterface:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.db = db
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إنشاء الواجهة المطورة
        self.create_modern_interface()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية المحسنة"""
        try:
            self.arabic_font_xl = font.Font(family="Segoe UI", size=18, weight="bold")
            self.arabic_font_lg = font.Font(family="Segoe UI", size=14, weight="bold")
            self.arabic_font_md = font.Font(family="Segoe UI", size=12)
            self.arabic_font_sm = font.Font(family="Segoe UI", size=10)
        except:
            self.arabic_font_xl = font.Font(size=18, weight="bold")
            self.arabic_font_lg = font.Font(size=14, weight="bold")
            self.arabic_font_md = font.Font(size=12)
            self.arabic_font_sm = font.Font(size=10)
    
    def create_modern_interface(self):
        """إنشاء الواجهة المطورة"""
        # مسح المحتوى الحالي
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # تكوين النافذة الرئيسية
        self.parent.configure(bg=advanced_ui.colors['bg_secondary'])
        
        # إنشاء الحاوي الرئيسي مع تمرير
        self.create_scrollable_container()
        
        # إنشاء الأقسام المطورة
        self.create_modern_header()
        self.create_welcome_section()
        self.create_quick_stats_cards()
        self.create_main_actions_grid()
        self.create_recent_activity_section()
        self.create_system_status_section()
    
    def create_scrollable_container(self):
        """إنشاء حاوي قابل للتمرير"""
        # Canvas للتمرير
        self.main_canvas = tk.Canvas(
            self.parent, 
            bg=advanced_ui.colors['bg_secondary'],
            highlightthickness=0
        )
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(
            self.parent, 
            orient="vertical", 
            command=self.main_canvas.yview
        )
        
        # الإطار القابل للتمرير
        self.scrollable_frame = tk.Frame(
            self.main_canvas, 
            bg=advanced_ui.colors['bg_secondary']
        )
        
        # ربط الأحداث
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))
        )
        
        self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط العناصر
        self.main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        self.main_canvas.bind_all("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """التعامل مع عجلة الماوس"""
        self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def create_modern_header(self):
        """إنشاء رأس الصفحة المطور"""
        # إنشاء خلفية متدرجة
        header_canvas = advanced_ui.create_gradient_frame(
            self.scrollable_frame,
            advanced_ui.colors['gradient_start'],
            advanced_ui.colors['gradient_end'],
            height=120
        )
        header_canvas.pack(fill='x', padx=20, pady=(20, 0))
        
        # محتوى الرأس
        header_content = tk.Frame(header_canvas, bg='transparent')
        header_content.place(relx=0.5, rely=0.5, anchor='center')
        
        # العنوان الرئيسي
        title_label = tk.Label(
            header_content,
            text="🏪 نظام إدارة المحل المتطور",
            font=self.arabic_font_xl,
            fg='white',
            bg='transparent'
        )
        title_label.pack(pady=(10, 5))
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_content,
            text="الإصدار 4.0 - واجهة حديثة ومتطورة",
            font=self.arabic_font_md,
            fg='#e2e8f0',
            bg='transparent'
        )
        subtitle_label.pack()
        
        # الوقت والتاريخ
        current_time = datetime.now()
        time_text = f"📅 {current_time.strftime('%Y-%m-%d')} | 🕐 {current_time.strftime('%H:%M')}"
        time_label = tk.Label(
            header_content,
            text=time_text,
            font=self.arabic_font_sm,
            fg='#cbd5e1',
            bg='transparent'
        )
        time_label.pack(pady=(5, 10))
    
    def create_welcome_section(self):
        """إنشاء قسم الترحيب"""
        welcome_card = advanced_ui.create_modern_card(
            self.scrollable_frame,
            title="مرحباً بك في نظام إدارة المحل",
            subtitle="ابدأ بإدارة أعمالك بكفاءة وسهولة"
        )
        welcome_card.pack(fill='x', padx=20, pady=20)
        
        # محتوى الترحيب
        content_frame = tk.Frame(welcome_card, bg=advanced_ui.colors['bg_card'])
        content_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        welcome_text = """
        🎯 إدارة شاملة للمبيعات والمشتريات
        📊 تقارير مفصلة وإحصائيات دقيقة  
        👥 إدارة العملاء والموردين
        📦 تتبع المخزون والمنتجات
        💰 إدارة مالية متكاملة
        """
        
        welcome_label = tk.Label(
            content_frame,
            text=welcome_text,
            font=self.arabic_font_md,
            fg=advanced_ui.colors['text_secondary'],
            bg=advanced_ui.colors['bg_card'],
            justify='right'
        )
        welcome_label.pack(anchor='e', pady=10)
    
    def create_quick_stats_cards(self):
        """إنشاء بطاقات الإحصائيات السريعة"""
        stats_frame = tk.Frame(self.scrollable_frame, bg=advanced_ui.colors['bg_secondary'])
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # عنوان القسم
        section_title = tk.Label(
            stats_frame,
            text="📊 إحصائيات سريعة",
            font=self.arabic_font_lg,
            fg=advanced_ui.colors['text_primary'],
            bg=advanced_ui.colors['bg_secondary']
        )
        section_title.pack(anchor='w', pady=(0, 15))
        
        # إطار البطاقات
        cards_container = tk.Frame(stats_frame, bg=advanced_ui.colors['bg_secondary'])
        cards_container.pack(fill='x')
        
        # بطاقات الإحصائيات
        self.create_stat_card(cards_container, "💰", "مبيعات اليوم", "0.00 جنيه", 'success', 'left')
        self.create_stat_card(cards_container, "📦", "المنتجات", "0 منتج", 'primary', 'left')
        self.create_stat_card(cards_container, "👥", "العملاء", "0 عميل", 'info', 'left')
        self.create_stat_card(cards_container, "⚠️", "تنبيهات", "0 تنبيه", 'warning', 'left')
    
    def create_stat_card(self, parent, icon, title, value, style, side):
        """إنشاء بطاقة إحصائية"""
        # ألوان البطاقات
        card_colors = {
            'primary': advanced_ui.colors['primary'],
            'success': advanced_ui.colors['success'],
            'info': advanced_ui.colors['info'],
            'warning': advanced_ui.colors['warning'],
            'danger': advanced_ui.colors['danger']
        }
        
        color = card_colors.get(style, advanced_ui.colors['primary'])
        
        # إطار البطاقة
        card_frame = tk.Frame(parent, bg=color, relief='flat', bd=0)
        card_frame.pack(side=side, fill='both', expand=True, padx=5, pady=5)
        
        # محتوى البطاقة
        content_frame = tk.Frame(card_frame, bg=color)
        content_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # الأيقونة
        icon_label = tk.Label(
            content_frame,
            text=icon,
            font=('Segoe UI Emoji', 24),
            fg='white',
            bg=color
        )
        icon_label.pack(anchor='w')
        
        # العنوان
        title_label = tk.Label(
            content_frame,
            text=title,
            font=self.arabic_font_md,
            fg='white',
            bg=color
        )
        title_label.pack(anchor='w', pady=(5, 0))
        
        # القيمة
        value_label = tk.Label(
            content_frame,
            text=value,
            font=self.arabic_font_lg,
            fg='white',
            bg=color
        )
        value_label.pack(anchor='w', pady=(2, 0))
        
        # تأثير التمرير
        def on_enter(e):
            card_frame.config(relief='raised', bd=2)
        
        def on_leave(e):
            card_frame.config(relief='flat', bd=0)
        
        card_frame.bind('<Enter>', on_enter)
        card_frame.bind('<Leave>', on_leave)
        
        return card_frame
    
    def create_main_actions_grid(self):
        """إنشاء شبكة الإجراءات الرئيسية"""
        actions_card = advanced_ui.create_modern_card(
            self.scrollable_frame,
            title="🚀 الإجراءات الرئيسية",
            subtitle="الوصول السريع للوظائف الأساسية"
        )
        actions_card.pack(fill='x', padx=20, pady=20)
        
        # شبكة الأزرار
        grid_frame = tk.Frame(actions_card, bg=advanced_ui.colors['bg_card'])
        grid_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # تكوين الشبكة
        for i in range(4):
            grid_frame.grid_columnconfigure(i, weight=1)
        
        # الأزرار الرئيسية
        buttons_data = [
            ("📊", "لوحة المعلومات", "dashboard", self.main_app.open_dashboard),
            ("💰", "نقطة البيع", "sales", self.main_app.open_pos_window),
            ("📦", "إدارة المنتجات", "products", self.main_app.open_products_window),
            ("🛒", "فاتورة شراء", "purchase", self.main_app.open_purchase_window),
            ("👥", "إدارة العملاء", "customers", self.main_app.open_customers_window),
            ("🏢", "إدارة الموردين", "suppliers", self.main_app.open_suppliers_window),
            ("📋", "التقارير", "reports", self.main_app.show_reports_menu),
            ("💸", "المصروفات", "expenses", self.main_app.open_expenses_window),
        ]
        
        for i, (icon, text, style, command) in enumerate(buttons_data):
            row = i // 4
            col = i % 4
            
            btn = advanced_ui.create_modern_button(
                grid_frame,
                text=text,
                style='outline',
                size='lg',
                command=command
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            # إضافة الأيقونة
            btn.config(text=f"{icon} {text}")
    
    def create_recent_activity_section(self):
        """إنشاء قسم الأنشطة الحديثة"""
        activity_card = advanced_ui.create_modern_card(
            self.scrollable_frame,
            title="🕐 الأنشطة الحديثة",
            subtitle="آخر العمليات والتحديثات"
        )
        activity_card.pack(fill='x', padx=20, pady=20)
        
        # قائمة الأنشطة
        activity_frame = tk.Frame(activity_card, bg=advanced_ui.colors['bg_card'])
        activity_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # أنشطة تجريبية
        activities = [
            ("💰", "تم إتمام عملية بيع بقيمة 150.00 جنيه", "منذ 5 دقائق"),
            ("📦", "تم إضافة منتج جديد: أرز أبيض", "منذ 15 دقيقة"),
            ("👤", "تم تسجيل عميل جديد: أحمد محمد", "منذ 30 دقيقة"),
            ("📋", "تم إنشاء تقرير المبيعات الشهري", "منذ ساعة"),
        ]
        
        for icon, text, time in activities:
            self.create_activity_item(activity_frame, icon, text, time)
    
    def create_activity_item(self, parent, icon, text, time):
        """إنشاء عنصر نشاط"""
        item_frame = tk.Frame(parent, bg=advanced_ui.colors['bg_card'])
        item_frame.pack(fill='x', pady=5)
        
        # الأيقونة
        icon_label = tk.Label(
            item_frame,
            text=icon,
            font=('Segoe UI Emoji', 16),
            bg=advanced_ui.colors['bg_card']
        )
        icon_label.pack(side='left', padx=(0, 10))
        
        # النص والوقت
        text_frame = tk.Frame(item_frame, bg=advanced_ui.colors['bg_card'])
        text_frame.pack(side='left', fill='x', expand=True)
        
        text_label = tk.Label(
            text_frame,
            text=text,
            font=self.arabic_font_md,
            fg=advanced_ui.colors['text_primary'],
            bg=advanced_ui.colors['bg_card'],
            anchor='w'
        )
        text_label.pack(anchor='w')
        
        time_label = tk.Label(
            text_frame,
            text=time,
            font=self.arabic_font_sm,
            fg=advanced_ui.colors['text_muted'],
            bg=advanced_ui.colors['bg_card'],
            anchor='w'
        )
        time_label.pack(anchor='w')
    
    def create_system_status_section(self):
        """إنشاء قسم حالة النظام"""
        status_card = advanced_ui.create_modern_card(
            self.scrollable_frame,
            title="⚙️ حالة النظام",
            subtitle="معلومات النظام والأداء"
        )
        status_card.pack(fill='x', padx=20, pady=(20, 40))
        
        # معلومات النظام
        status_frame = tk.Frame(status_card, bg=advanced_ui.colors['bg_card'])
        status_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        status_items = [
            ("🟢", "حالة قاعدة البيانات", "متصلة وتعمل بشكل طبيعي"),
            ("🟢", "حالة النظام", "يعمل بكفاءة عالية"),
            ("🟡", "آخر نسخة احتياطية", "منذ يوم واحد"),
            ("🔵", "إصدار النظام", "4.0 - الإصدار المطور"),
        ]
        
        for icon, title, status in status_items:
            self.create_status_item(status_frame, icon, title, status)
    
    def create_status_item(self, parent, icon, title, status):
        """إنشاء عنصر حالة"""
        item_frame = tk.Frame(parent, bg=advanced_ui.colors['bg_card'])
        item_frame.pack(fill='x', pady=3)
        
        # الأيقونة
        icon_label = tk.Label(
            item_frame,
            text=icon,
            font=('Segoe UI Emoji', 14),
            bg=advanced_ui.colors['bg_card']
        )
        icon_label.pack(side='left', padx=(0, 10))
        
        # العنوان
        title_label = tk.Label(
            item_frame,
            text=title,
            font=self.arabic_font_md,
            fg=advanced_ui.colors['text_primary'],
            bg=advanced_ui.colors['bg_card'],
            width=20,
            anchor='w'
        )
        title_label.pack(side='left')
        
        # الحالة
        status_label = tk.Label(
            item_frame,
            text=status,
            font=self.arabic_font_sm,
            fg=advanced_ui.colors['text_secondary'],
            bg=advanced_ui.colors['bg_card'],
            anchor='w'
        )
        status_label.pack(side='left', fill='x', expand=True)
