import tkinter as tk
from tkinter import ttk, messagebox, font
from datetime import datetime
from database import db

class ExpensesWindow:
    def __init__(self, parent):
        self.parent = parent
        self.db = db
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة المصروفات والإيرادات")
        self.window.geometry("1200x800")
        self.window.configure(bg='#f0f0f0')
        
        # تكوين الخط العربي
        self.setup_arabic_font()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_expenses()
        self.load_income()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.arabic_font_large = font.Font(family="Arial Unicode MS", size=14, weight="bold")
        except:
            self.arabic_font = font.Font(size=10)
            self.arabic_font_bold = font.Font(size=12, weight="bold")
            self.arabic_font_large = font.Font(size=14, weight="bold")
    
    def create_interface(self):
        """إنشاء واجهة المصروفات والإيرادات"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#e67e22', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="إدارة المصروفات والإيرادات", 
                              font=self.arabic_font_large, fg='white', bg='#e67e22')
        title_label.pack(expand=True)
        
        # إطار رئيسي
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # تبويب المصروفات
        self.expenses_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.expenses_frame, text="المصروفات")
        
        # تبويب الإيرادات
        self.income_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.income_frame, text="الإيرادات الأخرى")
        
        # تبويب الملخص
        self.summary_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.summary_frame, text="الملخص")
        
        # إنشاء محتوى التبويبات
        self.create_expenses_tab()
        self.create_income_tab()
        self.create_summary_tab()
    
    def create_expenses_tab(self):
        """إنشاء تبويب المصروفات"""
        # إطار النموذج (أعلى)
        form_frame = tk.LabelFrame(self.expenses_frame, text="إضافة مصروف جديد", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        form_frame.pack(fill='x', padx=10, pady=5)
        
        # إطار داخلي للنموذج
        inner_frame = tk.Frame(form_frame, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول
        row1_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=5)
        
        # فئة المصروف
        tk.Label(row1_frame, text="فئة المصروف:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.expense_category_combo = ttk.Combobox(row1_frame, font=self.arabic_font, width=20)
        self.expense_category_combo['values'] = [
            'رواتب وأجور', 'إيجار', 'كهرباء', 'مياه', 'غاز', 'هاتف وإنترنت',
            'صيانة', 'تأمين', 'ضرائب', 'مواصلات', 'دعاية وإعلان', 'أخرى'
        ]
        self.expense_category_combo.pack(side='left', padx=10)
        
        # المبلغ
        tk.Label(row1_frame, text="المبلغ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.expense_amount_entry = tk.Entry(row1_frame, font=self.arabic_font, width=15)
        self.expense_amount_entry.pack(side='left', padx=10)
        
        # طريقة الدفع
        tk.Label(row1_frame, text="طريقة الدفع:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.expense_payment_combo = ttk.Combobox(row1_frame, font=self.arabic_font, width=15)
        self.expense_payment_combo['values'] = ['نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان']
        self.expense_payment_combo.set('نقدي')
        self.expense_payment_combo.pack(side='left', padx=10)
        
        # الصف الثاني
        row2_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=5)
        
        # الوصف
        tk.Label(row2_frame, text="الوصف:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.expense_description_entry = tk.Entry(row2_frame, font=self.arabic_font, width=40)
        self.expense_description_entry.pack(side='left', padx=10)
        
        # الملاحظات
        tk.Label(row2_frame, text="ملاحظات:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.expense_notes_entry = tk.Entry(row2_frame, font=self.arabic_font, width=30)
        self.expense_notes_entry.pack(side='left', padx=10)
        
        # أزرار المصروفات
        expense_buttons_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        expense_buttons_frame.pack(pady=10)
        
        tk.Button(expense_buttons_frame, text="إضافة مصروف", font=self.arabic_font_bold,
                 bg='#e74c3c', fg='white', width=12,
                 command=self.add_expense).pack(side='left', padx=5)
        
        tk.Button(expense_buttons_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', width=10,
                 command=self.update_expense, state='disabled').pack(side='left', padx=5)
        
        tk.Button(expense_buttons_frame, text="حذف", font=self.arabic_font,
                 bg='#95a5a6', fg='white', width=10,
                 command=self.delete_expense, state='disabled').pack(side='left', padx=5)
        
        tk.Button(expense_buttons_frame, text="مسح", font=self.arabic_font,
                 bg='#34495e', fg='white', width=10,
                 command=self.clear_expense_form).pack(side='left', padx=5)
        
        # قائمة المصروفات
        list_frame = tk.LabelFrame(self.expenses_frame, text="قائمة المصروفات", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # جدول المصروفات
        columns = ('ID', 'التاريخ', 'الفئة', 'الوصف', 'المبلغ', 'طريقة الدفع', 'ملاحظات')
        self.expenses_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        for col in columns:
            self.expenses_tree.heading(col, text=col)
        
        self.expenses_tree.column('ID', width=50)
        self.expenses_tree.column('التاريخ', width=100)
        self.expenses_tree.column('الفئة', width=120)
        self.expenses_tree.column('الوصف', width=200)
        self.expenses_tree.column('المبلغ', width=100)
        self.expenses_tree.column('طريقة الدفع', width=100)
        self.expenses_tree.column('ملاحظات', width=150)
        
        # شريط التمرير للمصروفات
        scrollbar_expenses = ttk.Scrollbar(list_frame, orient='vertical', command=self.expenses_tree.yview)
        self.expenses_tree.configure(yscrollcommand=scrollbar_expenses.set)
        
        self.expenses_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_expenses.pack(side='right', fill='y')
        
        # ربط النقر على المصروف
        self.expenses_tree.bind('<ButtonRelease-1>', self.on_expense_select)
        
        # متغير لحفظ ID المصروف المحدد
        self.selected_expense_id = None
    
    def create_income_tab(self):
        """إنشاء تبويب الإيرادات"""
        # إطار النموذج (أعلى)
        form_frame = tk.LabelFrame(self.income_frame, text="إضافة إيراد جديد", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        form_frame.pack(fill='x', padx=10, pady=5)
        
        # إطار داخلي للنموذج
        inner_frame = tk.Frame(form_frame, bg='#f0f0f0')
        inner_frame.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول
        row1_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row1_frame.pack(fill='x', pady=5)
        
        # مصدر الإيراد
        tk.Label(row1_frame, text="مصدر الإيراد:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.income_source_combo = ttk.Combobox(row1_frame, font=self.arabic_font, width=20)
        self.income_source_combo['values'] = [
            'خدمات إضافية', 'عمولات', 'فوائد بنكية', 'إيجار', 'استثمارات', 'أخرى'
        ]
        self.income_source_combo.pack(side='left', padx=10)
        
        # المبلغ
        tk.Label(row1_frame, text="المبلغ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.income_amount_entry = tk.Entry(row1_frame, font=self.arabic_font, width=15)
        self.income_amount_entry.pack(side='left', padx=10)
        
        # طريقة الاستلام
        tk.Label(row1_frame, text="طريقة الاستلام:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.income_payment_combo = ttk.Combobox(row1_frame, font=self.arabic_font, width=15)
        self.income_payment_combo['values'] = ['نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان']
        self.income_payment_combo.set('نقدي')
        self.income_payment_combo.pack(side='left', padx=10)
        
        # الصف الثاني
        row2_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        row2_frame.pack(fill='x', pady=5)
        
        # الوصف
        tk.Label(row2_frame, text="الوصف:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.income_description_entry = tk.Entry(row2_frame, font=self.arabic_font, width=40)
        self.income_description_entry.pack(side='left', padx=10)
        
        # الملاحظات
        tk.Label(row2_frame, text="ملاحظات:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.income_notes_entry = tk.Entry(row2_frame, font=self.arabic_font, width=30)
        self.income_notes_entry.pack(side='left', padx=10)
        
        # أزرار الإيرادات
        income_buttons_frame = tk.Frame(inner_frame, bg='#f0f0f0')
        income_buttons_frame.pack(pady=10)
        
        tk.Button(income_buttons_frame, text="إضافة إيراد", font=self.arabic_font_bold,
                 bg='#27ae60', fg='white', width=12,
                 command=self.add_income).pack(side='left', padx=5)
        
        tk.Button(income_buttons_frame, text="تحديث", font=self.arabic_font,
                 bg='#3498db', fg='white', width=10,
                 command=self.update_income, state='disabled').pack(side='left', padx=5)
        
        tk.Button(income_buttons_frame, text="حذف", font=self.arabic_font,
                 bg='#95a5a6', fg='white', width=10,
                 command=self.delete_income, state='disabled').pack(side='left', padx=5)
        
        tk.Button(income_buttons_frame, text="مسح", font=self.arabic_font,
                 bg='#34495e', fg='white', width=10,
                 command=self.clear_income_form).pack(side='left', padx=5)
        
        # قائمة الإيرادات
        list_frame = tk.LabelFrame(self.income_frame, text="قائمة الإيرادات الأخرى", 
                                  font=self.arabic_font_bold, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # جدول الإيرادات
        columns = ('ID', 'التاريخ', 'المصدر', 'الوصف', 'المبلغ', 'طريقة الاستلام', 'ملاحظات')
        self.income_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        for col in columns:
            self.income_tree.heading(col, text=col)
        
        self.income_tree.column('ID', width=50)
        self.income_tree.column('التاريخ', width=100)
        self.income_tree.column('المصدر', width=120)
        self.income_tree.column('الوصف', width=200)
        self.income_tree.column('المبلغ', width=100)
        self.income_tree.column('طريقة الاستلام', width=120)
        self.income_tree.column('ملاحظات', width=150)
        
        # شريط التمرير للإيرادات
        scrollbar_income = ttk.Scrollbar(list_frame, orient='vertical', command=self.income_tree.yview)
        self.income_tree.configure(yscrollcommand=scrollbar_income.set)
        
        self.income_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar_income.pack(side='right', fill='y')
        
        # ربط النقر على الإيراد
        self.income_tree.bind('<ButtonRelease-1>', self.on_income_select)
        
        # متغير لحفظ ID الإيراد المحدد
        self.selected_income_id = None
    
    def create_summary_tab(self):
        """إنشاء تبويب الملخص"""
        # إطار الفترة الزمنية
        period_frame = tk.LabelFrame(self.summary_frame, text="اختيار الفترة", 
                                    font=self.arabic_font_bold, bg='#f0f0f0')
        period_frame.pack(fill='x', padx=10, pady=5)
        
        period_inner = tk.Frame(period_frame, bg='#f0f0f0')
        period_inner.pack(fill='x', padx=10, pady=10)
        
        # من تاريخ
        tk.Label(period_inner, text="من تاريخ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left')
        self.summary_start_date = tk.Entry(period_inner, font=self.arabic_font, width=12)
        self.summary_start_date.pack(side='left', padx=5)
        self.summary_start_date.insert(0, datetime.now().replace(day=1).strftime("%Y-%m-%d"))
        
        # إلى تاريخ
        tk.Label(period_inner, text="إلى تاريخ:", font=self.arabic_font, bg='#f0f0f0').pack(side='left', padx=(20, 0))
        self.summary_end_date = tk.Entry(period_inner, font=self.arabic_font, width=12)
        self.summary_end_date.pack(side='left', padx=5)
        self.summary_end_date.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        # زر التحديث
        tk.Button(period_inner, text="تحديث الملخص", font=self.arabic_font,
                 bg='#3498db', fg='white', command=self.update_summary).pack(side='left', padx=20)
        
        # إطار الملخص
        summary_display_frame = tk.LabelFrame(self.summary_frame, text="ملخص المصروفات والإيرادات", 
                                             font=self.arabic_font_bold, bg='#f0f0f0')
        summary_display_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # نص الملخص
        self.summary_text = tk.Text(summary_display_frame, font=self.arabic_font, height=20, 
                                   bg='#ecf0f1', state='disabled')
        self.summary_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تحديث الملخص الأولي
        self.update_summary()

    def load_expenses(self):
        """تحميل المصروفات"""
        # مسح الجدول
        for item in self.expenses_tree.get_children():
            self.expenses_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, created_at, category, description, amount, payment_method, notes
                FROM expenses
                ORDER BY created_at DESC
            ''')

            expenses = cursor.fetchall()

            for expense in expenses:
                self.expenses_tree.insert('', 'end', values=(
                    expense[0],  # id
                    expense[1][:10],  # created_at (date only)
                    expense[2],  # category
                    expense[3],  # description
                    f"{expense[4]:.2f}",  # amount
                    expense[5],  # payment_method
                    expense[6] or ''  # notes
                ))

            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المصروفات: {str(e)}")

    def load_income(self):
        """تحميل الإيرادات"""
        # مسح الجدول
        for item in self.income_tree.get_children():
            self.income_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, created_at, source, description, amount, payment_method, notes
                FROM other_income
                ORDER BY created_at DESC
            ''')

            income_records = cursor.fetchall()

            for income in income_records:
                self.income_tree.insert('', 'end', values=(
                    income[0],  # id
                    income[1][:10],  # created_at (date only)
                    income[2],  # source
                    income[3],  # description
                    f"{income[4]:.2f}",  # amount
                    income[5],  # payment_method
                    income[6] or ''  # notes
                ))

            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الإيرادات: {str(e)}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        if not self.validate_expense_form():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO expenses (category, description, amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self.expense_category_combo.get(),
                self.expense_description_entry.get(),
                float(self.expense_amount_entry.get()),
                self.expense_payment_combo.get(),
                self.expense_notes_entry.get()
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة المصروف بنجاح")
            self.clear_expense_form()
            self.load_expenses()
            self.update_summary()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المصروف: {str(e)}")

    def add_income(self):
        """إضافة إيراد جديد"""
        if not self.validate_income_form():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO other_income (source, description, amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self.income_source_combo.get(),
                self.income_description_entry.get(),
                float(self.income_amount_entry.get()),
                self.income_payment_combo.get(),
                self.income_notes_entry.get()
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة الإيراد بنجاح")
            self.clear_income_form()
            self.load_income()
            self.update_summary()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الإيراد: {str(e)}")

    def validate_expense_form(self):
        """التحقق من صحة نموذج المصروف"""
        if not self.expense_category_combo.get():
            messagebox.showerror("خطأ", "يجب اختيار فئة المصروف")
            return False

        if not self.expense_description_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال وصف المصروف")
            return False

        try:
            amount = float(self.expense_amount_entry.get())
            if amount <= 0:
                messagebox.showerror("خطأ", "المبلغ يجب أن يكون أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "المبلغ يجب أن يكون رقماً صحيحاً")
            return False

        if not self.expense_payment_combo.get():
            messagebox.showerror("خطأ", "يجب اختيار طريقة الدفع")
            return False

        return True

    def validate_income_form(self):
        """التحقق من صحة نموذج الإيراد"""
        if not self.income_source_combo.get():
            messagebox.showerror("خطأ", "يجب اختيار مصدر الإيراد")
            return False

        if not self.income_description_entry.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال وصف الإيراد")
            return False

        try:
            amount = float(self.income_amount_entry.get())
            if amount <= 0:
                messagebox.showerror("خطأ", "المبلغ يجب أن يكون أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "المبلغ يجب أن يكون رقماً صحيحاً")
            return False

        if not self.income_payment_combo.get():
            messagebox.showerror("خطأ", "يجب اختيار طريقة الاستلام")
            return False

        return True

    def clear_expense_form(self):
        """مسح نموذج المصروف"""
        self.expense_category_combo.set('')
        self.expense_description_entry.delete(0, 'end')
        self.expense_amount_entry.delete(0, 'end')
        self.expense_payment_combo.set('نقدي')
        self.expense_notes_entry.delete(0, 'end')
        self.selected_expense_id = None

    def clear_income_form(self):
        """مسح نموذج الإيراد"""
        self.income_source_combo.set('')
        self.income_description_entry.delete(0, 'end')
        self.income_amount_entry.delete(0, 'end')
        self.income_payment_combo.set('نقدي')
        self.income_notes_entry.delete(0, 'end')
        self.selected_income_id = None

    def on_expense_select(self, event):
        """عند تحديد مصروف من القائمة"""
        selection = self.expenses_tree.selection()
        if selection:
            item = self.expenses_tree.item(selection[0])
            expense_data = item['values']

            self.selected_expense_id = expense_data[0]

            # ملء النموذج
            self.expense_category_combo.set(expense_data[2])
            self.expense_description_entry.delete(0, 'end')
            self.expense_description_entry.insert(0, expense_data[3])
            self.expense_amount_entry.delete(0, 'end')
            self.expense_amount_entry.insert(0, expense_data[4])
            self.expense_payment_combo.set(expense_data[5])
            self.expense_notes_entry.delete(0, 'end')
            self.expense_notes_entry.insert(0, expense_data[6])

    def on_income_select(self, event):
        """عند تحديد إيراد من القائمة"""
        selection = self.income_tree.selection()
        if selection:
            item = self.income_tree.item(selection[0])
            income_data = item['values']

            self.selected_income_id = income_data[0]

            # ملء النموذج
            self.income_source_combo.set(income_data[2])
            self.income_description_entry.delete(0, 'end')
            self.income_description_entry.insert(0, income_data[3])
            self.income_amount_entry.delete(0, 'end')
            self.income_amount_entry.insert(0, income_data[4])
            self.income_payment_combo.set(income_data[5])
            self.income_notes_entry.delete(0, 'end')
            self.income_notes_entry.insert(0, income_data[6])

    def update_expense(self):
        """تحديث مصروف"""
        messagebox.showinfo("قريباً", "ميزة تحديث المصروف قيد التطوير")

    def update_income(self):
        """تحديث إيراد"""
        messagebox.showinfo("قريباً", "ميزة تحديث الإيراد قيد التطوير")

    def delete_expense(self):
        """حذف مصروف"""
        messagebox.showinfo("قريباً", "ميزة حذف المصروف قيد التطوير")

    def delete_income(self):
        """حذف إيراد"""
        messagebox.showinfo("قريباً", "ميزة حذف الإيراد قيد التطوير")

    def update_summary(self):
        """تحديث ملخص المصروفات والإيرادات"""
        try:
            start_date = self.summary_start_date.get()
            end_date = self.summary_end_date.get()

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # حساب إجمالي المصروفات
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0) as total_expenses
                FROM expenses
                WHERE DATE(created_at) BETWEEN ? AND ?
            ''', (start_date, end_date))
            total_expenses = cursor.fetchone()[0]

            # حساب إجمالي الإيرادات الأخرى
            cursor.execute('''
                SELECT COALESCE(SUM(amount), 0) as total_other_income
                FROM other_income
                WHERE DATE(created_at) BETWEEN ? AND ?
            ''', (start_date, end_date))
            total_other_income = cursor.fetchone()[0]

            # حساب المصروفات حسب الفئة
            cursor.execute('''
                SELECT category, SUM(amount) as total
                FROM expenses
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY category
                ORDER BY total DESC
            ''', (start_date, end_date))
            expenses_by_category = cursor.fetchall()

            # حساب الإيرادات حسب المصدر
            cursor.execute('''
                SELECT source, SUM(amount) as total
                FROM other_income
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY source
                ORDER BY total DESC
            ''', (start_date, end_date))
            income_by_source = cursor.fetchall()

            # إنشاء نص الملخص
            summary = f"""
الفترة: من {start_date} إلى {end_date}

═══════════════════════════════════════════════════════════════

📊 الملخص العام:
• إجمالي المصروفات: {total_expenses:.2f} جنيه
• إجمالي الإيرادات الأخرى: {total_other_income:.2f} جنيه
• صافي الإيرادات الأخرى: {(total_other_income - total_expenses):.2f} جنيه

═══════════════════════════════════════════════════════════════

💰 المصروفات حسب الفئة:
"""

            if expenses_by_category:
                for category, amount in expenses_by_category:
                    percentage = (amount / total_expenses * 100) if total_expenses > 0 else 0
                    summary += f"• {category}: {amount:.2f} جنيه ({percentage:.1f}%)\n"
            else:
                summary += "• لا توجد مصروفات في هذه الفترة\n"

            summary += "\n═══════════════════════════════════════════════════════════════\n\n"
            summary += "📈 الإيرادات الأخرى حسب المصدر:\n"

            if income_by_source:
                for source, amount in income_by_source:
                    percentage = (amount / total_other_income * 100) if total_other_income > 0 else 0
                    summary += f"• {source}: {amount:.2f} جنيه ({percentage:.1f}%)\n"
            else:
                summary += "• لا توجد إيرادات أخرى في هذه الفترة\n"

            # عرض الملخص
            self.summary_text.config(state='normal')
            self.summary_text.delete('1.0', 'end')
            self.summary_text.insert('1.0', summary)
            self.summary_text.config(state='disabled')

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الملخص: {str(e)}")
